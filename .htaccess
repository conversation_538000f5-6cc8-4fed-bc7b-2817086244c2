<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On
    DirectoryIndex index.php

    RewriteRule ^assets/([^/]+)/(.+)$ /resources/themes/web/$1/public/$2 [L]
    RewriteRule ^admin-assets/([^/]+)/(.+)$ /resources/themes/admin/$1/public/$2 [L]
    RewriteRule ^email-assets/([^/]+)/(.+)$ /resources/themes/email/$1/public/$2 [L]
    RewriteRule ^plugin-assets/([^/]+)/(.+)$ /resources/plugins/$1/public/$2 [L]

    # 404 for non-existent assets
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(assets|admin-assets|email-assets|plugin-assets)/([^/]+)/(.+)$ - [R=404,L]

    # Redirect trailing slashes to non-trailing (except directories)
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)/$ /$1 [L,R=301]

    # Fallback to index.php if the file or directory doesn't exist
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]

    # Pass Authorization header to PHP
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
</IfModule>
