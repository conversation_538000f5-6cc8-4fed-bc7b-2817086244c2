# Zen Framework Documentation

Welcome to the Zen Framework documentation! This guide will walk you through the core concepts and functionalities of Zen, helping you build robust web applications efficiently.

---

## 1. Project Structure

The Zen Framework follows a clear and intuitive directory structure:

-   `app/`: Contains your application's core logic (controllers, models, migrations, routes, layers).
    -   `app/Controller/`: Your application's controllers.
    -   `app/Database/migrations/`: Database migration files.
    -   `app/Model/`: Your application's models.
    -   `app/auth.php`: Authentication configuration.
    -   `app/config.php`: General application configuration.
    -   `app/layers.php`: Middleware definitions.
    -   `app/preload.php`: Files to be preloaded.
    -   `app/router.php`: Route definitions.
-   `core/`: The Zen Framework's core files (engine, database abstraction, CLI, etc.).
-   `resources/`: Assets like languages, plugins, and themes.
    -   `resources/languages/`: Application language files.
    -   `resources/plugins/`: Third-party plugins.
    -   `resources/themes/`: Your application's themes (views, public assets).
-   `storage/`: Writable directories for cache, logs, and secured files.
-   `vendor/`: Composer dependencies.
-   `zen`: The CLI executable.
-   `index.php`: The application's entry point.

---

## 2. Routing

Zen's routing system allows you to define how your application responds to HTTP requests. Routes are defined in `app/router.php`.

### Basic Routing

You can define routes using the `Router::set()` method:

```php
<?php

use Zen\Core\Router;

// Define a GET route
Router::set('GET', '/', 'App\Controller\Frontpage::index', 'frontpage');

// Define a POST route
Router::set('POST', '/submit', 'App\Controller\Contact::submit', 'contact.submit');

// Route with parameters
Router::set('GET', '/users/[i:id]', 'App\Controller\User::show', 'user.show');
// [i:id] matches an integer parameter named 'id'
// [a:slug] matches an alphanumeric parameter named 'slug'
// [*:all] matches any character until the next slash or end of string
// [h:hash] matches a hexadecimal string
```

-   **Method:** The HTTP method (GET, POST, PUT, DELETE, etc.).
-   **Path:** The URL path.
-   **Handler:** The controller and method to execute (e.g., `'App\Controller\Frontpage::index'`).
-   **Name:** A unique name for the route, useful for generating URLs.
-   **Layers (Optional):** An array of middleware layers to apply to the route.

### Route Groups

You can group routes that share common attributes like a base path or middleware:

```php
Router::group('/admin', function () {
    Router::set('GET', '/', 'App\Controller\Admin\Dashboard::index', 'admin.dashboard');
    Router::set('GET', '/users', 'App\Controller\Admin\User::list', 'admin.users');
}, ['AuthAdmin']); // Apply 'AuthAdmin' middleware to all routes in this group
```

---

## 3. Controllers

Controllers handle incoming requests and return responses. They are located in `app/Controller/`.

### Creating a Controller

You can create a new controller using the CLI:

```bash
php zen make:controller --name=UserController
```

This will create `app/Controller/UserController.php`:

```php
<?php

namespace App\Controller;

use Zen\Core\Controller;

class UserController extends Controller
{
    public function index()
    {
        // Handle the request
    }

    public function show($id)
    {
        // Handle showing a specific user
    }
}
```

### Controller Methods

Methods within your controller will correspond to your routes. Parameters from the route path are passed as arguments to the method.

---

## 4. Models

Models represent your application's data and business logic. They are located in `app/Model/`.

### Creating a Model

You can create a new model using the CLI:

```bash
php zen make:model --name=Product
```

This will create `app/Model/Product.php`:

```php
<?php

namespace App\Model;

use Zen\Core\Model;

class Product extends Model
{
    protected $table = 'products'; // The database table associated with the model

    // protected $fillable = ['name', 'price']; // Fields that can be mass-assigned

    // Add your properties and methods here
}
```

### Basic Model Usage

Zen's `Model` class provides basic database interaction.

```php
<?php

use App\Model\User;

// Find a user by ID
$user = User::find(1);

// Get all users
$users = User::all();

// Create a new user
$newUser = User::create(['name' => 'John Doe', 'email' => '<EMAIL>']);

// Update a user
$user->update(['name' => 'Jane Doe']);

// Delete a user
$user->delete();
```

---

## 5. Database

Zen uses Doctrine DBAL for database abstraction, providing a powerful and flexible way to interact with your database.

### Configuration

Database connection settings are defined in `app/config.php`:

```php
<?php

return [
    'database' => [
        'driver'   => 'pdo_mysql', // or pdo_sqlite, pdo_pgsql, pdo_sqlsrv
        'host'     => 'localhost',
        'dbname'   => 'your_database_name',
        'user'     => 'your_username',
        'password' => 'your_password',
        'charset'  => 'utf8mb4',
    ],
    // ... other configurations
];
```

### Migrations

Migrations allow you to manage your database schema in a version-controlled way. Migration files are located in `app/Database/migrations/`.

#### Creating a Migration

```bash
php zen make:migration --name=CreateUsersTable
```

This will create a file like `app/Database/migrations/2025_07_10_191647_CreateUsersTable.php`:

```php
<?php

namespace App\Database\Migrations;

use Doctrine\DBAL\Schema\Schema;

class CreateUsersTable
{
    public function up(Schema $schema): void
    {
        // Define your table creation logic
        $table = $schema->createTable('users');
        $table->addColumn('id', 'integer', ['unsigned' => true, 'autoincrement' => true]);
        $table->addColumn('name', 'string', ['length' => 255]);
        $table->addColumn('email', 'string', ['length' => 255]);
        $table->setPrimaryKey(['id']);
    }

    public function down(Schema $schema): void
    {
        // Define your table rollback logic
        $schema->dropTable('users');
    }
}
```

#### Running Migrations

To run all pending migrations:

```bash
php zen migrate
```

#### Rolling Back Migrations

To rollback the last migration:

```bash
php zen migrate:rollback
```

To rollback multiple migrations:

```bash
php zen migrate:rollback --steps=3
```

### Database Interaction (Db Class)

You can directly interact with the database using the `Zen\Core\Db` class, which provides access to the Doctrine DBAL Connection.

```php
<?php

use Zen\Core\Db;

// Get the DBAL connection
$connection = Db::dbal();

// Execute a raw SQL query
$connection->executeQuery('SELECT * FROM users');

// Using the Query Builder
$queryBuilder = $connection->createQueryBuilder();
$users = $queryBuilder
    ->select('*')
    ->from('users')
    ->where('id = :id')
    ->setParameter('id', 1)
    ->fetchAllAssociative();
```

---

## 6. Assets and Public Folders

Zen uses a simple templating system based on PHP files within your themes. Themes are located in `resources/themes/`.

### Theme Structure

A typical theme structure might look like this:

```
resources/themes/
└── web/
    └── default/
        ├── account.html
        ├── auth.html
        ├── welcome.html
        ├── languages/
        │   └── en/
        │       └── language.php
        └── public/
            ├── css/
            ├── js/
            └── images/
```

### Public Assets in Themes and Plugins

Zen Framework provides a convenient way to serve static assets (CSS, JavaScript, images, etc.) directly from your themes and plugins. Each theme (e.g., `web`, `email`, `admin`) and plugin can have a `public/` directory.

These `public/` directories are automatically mapped to specific URLs, making it easy to link to your assets in your views.

#### Accessing Theme Assets

Assets within a theme's `public/` folder are typically accessed via a URL structure that reflects the theme type and name. For example:

-   **Web Theme (`resources/themes/web/<theme_name>/public/`)**: Accessed via `/assets/<theme_name>/<path_to_asset>`.
    -   Example: `resources/themes/web/default/public/css/style.css` -> `/assets/default/css/style.css`

-   **Admin Theme (`resources/themes/admin/<theme_name>/public/`)**: Accessed via `/admin-assets/<theme_name>/<path_to_asset>`.
    -   Example: `resources/themes/admin/default/js/admin.js` -> `/admin-assets/default/js/admin.js`

-   **Email Theme (`resources/themes/email/<theme_name>/public/`)**: Accessed via `/email-assets/<theme_name>/<path_to_asset>`.
    -   Example: `resources/themes/email/default/images/logo.png` -> `/email-assets/default/images/logo.png`

#### Accessing Plugin Assets

Assets within a plugin's `public/` folder (`resources/plugins/<plugin_name>/public/`) are typically accessed via a URL structure like `/plugin-assets/<plugin_name>/<path_to_asset>`.

-   Example: `resources/plugins/paypal/public/js/paypal.js` -> `/plugin-assets/paypal/js/paypal.js`

This structured approach ensures that your static assets are easily discoverable and accessible within your application.

### Rendering Views

You can render views from your controllers using the `Controller::view()` method:

```php
<?php

namespace App\Controller;

use Zen\Core\Controller;

class Frontpage extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Welcome to Zen!',
            'message' => 'This is your first Zen application.'
        ];

        // Renders resources/themes/web/default/welcome.html
        $this->view('welcome', $data);
    }
}
```

Data passed to the `view()` method is available as variables in your view file.

---

## 7. Layers (Middleware)

Layers (middleware) provide a convenient mechanism for filtering HTTP requests entering your application. They are defined in `app/layers.php`.

### Creating a Layer

You can create a new layer using the CLI:

```bash
php zen make:layer --name=Auth
```

This will add a new layer definition to `app/layers.php`:

```php
<?php

use Zen\Core\Layer;
use Zen\Core\Error;

Layer::set('Auth', function () {
    $checkpoint = true; // Your authentication logic here
    if (!$checkpoint) {
        Error::print("Authentication required", false);
        return false; // Stop request processing
    }
    return true; // Continue request processing
});
```

### Applying Layers

Layers can be applied to routes or route groups:

```php
// Apply 'Auth' layer to a single route
Router::set('GET', '/dashboard', 'App\Controller\Dashboard::index', 'dashboard', ['Auth']);

// Apply 'AuthAdmin' layer to a route group
Router::group('/admin', function () {
    // ... admin routes
}, ['AuthAdmin']);
```

---

## 8. CLI Commands

Zen provides a set of command-line interface (CLI) tools to help you with common development tasks. You can run them using `php zen <command>`.

-   `php zen serve`: Start the development server.
    -   `--port=8000`: Specify the port.
    -   `--host=127.0.0.1`: Specify the host.
-   `php zen make:controller --name=MyController`: Create a new controller.
-   `php zen make:model --name=MyModel`: Create a new model.
-   `php zen make:layer --name=MyLayer`: Create a new middleware layer.
-   `php zen make:migration --name=CreateMyTable`: Create a new database migration.
-   `php zen migrate`: Run database migrations.
-   `php zen migrate:rollback [--steps=N]`: Rollback the last N database migrations (default N=1).
-   `php zen list:routes`: List all registered routes.
-   `php zen list:layers`: List all registered layers (middleware).
-   `php zen setup:auth`: Set up the authentication system.
-   `php zen reset`: Reset the application to its initial state (use with caution!).

---

## 9. Error Handling

Zen provides basic error handling through the `Zen\Core\Error` class.

```php
<?php

use Zen\Core\Error;

// Print an error message and optionally stop execution
Error::print("Something went wrong!", true); // true to die, false to continue
```

---

## 10. Helpers

The `core/Helpers.php` file contains a collection of global helper functions that you can use throughout your application for common tasks. Review this file to discover available utilities.

---

## 11. Authentication (`Zen\Core\Auth`)

Zen provides a robust authentication system to manage user logins, sessions, and access control.

### Basic Usage

```php
<?php

use Zen\Core\Auth;

// Check if a user is logged in
if (Auth::check()) {
    echo "User is logged in.";
}

// Get the currently authenticated user's ID
$userId = Auth::id();

// Get the currently authenticated user's data
$user = Auth::user();

// Attempt to log in a user
if (Auth::attempt('username', 'password')) {
    echo "Login successful!";
} else {
    echo "Invalid credentials.";
}

// Log out the current user
Auth::logout();
```

### CLI Setup

You can set up the basic authentication system using the CLI:

```bash
php zen setup:auth
```

This command will guide you through creating necessary controllers, models, and middleware for authentication.

---

## 12. Pagination (`Zen\Core\Pagination`)

The `Pagination` class helps you easily paginate results from your database queries, often used in conjunction with your Models.

### Basic Usage

```php
<?php

use App\Model\User; // Assuming you have a User model

// Get paginated users directly from the User model
// The paginate method returns a Pagination instance.
// ->performanceMode() optimizes the query for large datasets.
// ->setPageParam('p', true) sets the URL query parameter for pagination to 'p'.
// ->fetch('*') executes the query and fetches all columns.
$pagination = User::paginate(2)->performanceMode()->setPageParam('p', true)->fetch('*');

// Get items for the current page
$items = $pagination->getItems();

// Example: Render items fetched for the current page
foreach ($items as $item) {
    echo $item['username'] . '<br>';
}

// Display pagination links using a dedicated view file (e.g., resources/themes/web/default/pagination.html)
echo $pagination->render('pagination');

// Display pagination links using a custom pattern, providing full control over HTML structure
echo $pagination->renderPattern([
    'container' => '<ul>:loop:</ul>', // Wrapper for all pagination links
    'item' => '<li><a href=":url:">:label:</a></li>', // Individual page link
    'active' => '<li class="active"><a href="javascript:void(0);">:label:</a></li>', // Active page link
    'disabled' => '<li class="disabled"><span>:label:</span></li>', // Disabled page link (e.g., ellipsis)
    'prev' => '<li><a href=":url:">&laquo;</a></li>', // Previous page link
    'next' => '<li><a href=":url:">&raquo;</a></li>', // Next page link
    'prev_disabled' => '<li class="disabled"><span>&laquo;</span></li>', // Disabled previous link
    'next_disabled' => '<li class="disabled"><span>&raquo;</span></li>' // Disabled next link
]);

// Display pagination links manually, accessing the raw links array
$links = $pagination->links();

echo '<a href="' . $links['prev']['url'] . '">' . $links['prev']['label'] . '</a> ';
foreach ($links['links'] as $link) {
    if (isset($link['active']) && $link['active']) {
        echo '<a href="javascript:;">' . $link['label'] . '</a> ';
    } elseif ($link['url']) {
        echo '<a href="' . $link['url'] . '">' . $link['label'] . '</a> ';
    } else {
        echo '<a href="javascript:;">' . $link['label'] . '</a> ';
    }
}
echo '<a href="' . $links['next']['url'] . '">' . $links['next']['label'] . '</a> ';
```

---

## 13. Languages (`Zen\Core\Language`)

Zen provides a simple way to manage multi-language support for your application. Language files are located in 
`resources/languages/<language>/`
`resources/themes/<theme_type>/<theme_name>/languages/<language>/`
`resources/plugins/<plugin_name>/languages/<language>/`

### Loading Language Files

Language files are typically PHP arrays. For example, `resources/languages/en/language.php`:

```php
<?php

return [
    'welcome' => 'Welcome to our application!',
    'greeting' => 'Hello, :name!',
];
```

### Retrieving Translated Strings

```php
<?php

use Zen\Core\Language;

// Set the current language (e.g., based on user preference or URL segment)
Language::setDefaultLanguage('es'); // Assuming you have 'resources/languages/es/language.php'


// Get a simple translation from 'resources/languages/en/language.php'
echo Language::get('welcome'); // Output: Welcome to our application!
// or 
echo l('welcome');
// or without echo
_l('welcome');

// From 'resources/languages/en/dashboard/websites/language.php'
echo l('dashboard/websites/website_description');

// From 'resources/languages/en/dashboard/websites/anyfile_name.php'
echo l('dashboard/websites/anyfile_name:website_description');

// Get a translation from a theme eg: 'resources/themes/web/default/languages/en/language.php'
_l('welcome', 'welcome to our application!', "theme:web:default");

// Get a translation from a plugin eg: 'resources/plugins/paypal/languages/en/language.php'
_l('pwelcome', 'welcome to our application!', "plugin:paypal");

// Get a translation from 'resources/plugins/paypal/languages/en/dashboard/sections/payments.php'
// return [
//     'payment_description' => 'Translation goes here',
//     ...
// ];
_l('dashboard/sections/payments:payment_description', 'Default content here', "plugin:paypal");

// Get current language info
Language::info(); 
```

---

## 14. Mail (`Zen\Core\Mail`)

The `Mail` class allows you to send emails from your application. It typically uses PHPMailer under the hood.

### Configuration

Mail settings are usually configured in `app/config.php` or through the `storage/secured/settings/data/smtp.data.php` file.

```php
<?php

return [
    'mail' => [
        'host'       => 'smtp.example.com',
        'port'       => 587,
        'username'   => '<EMAIL>',
        'password'   => 'your_email_password',
        'encryption' => 'tls', // or 'ssl'
        'from_email' => '<EMAIL>',
        'from_name'  => 'Your Application',
    ],
    // ...
];
```

### Sending Emails

```php
<?php

use Zen\Core\Mail;

// Send a simple text email
Mail::send('<EMAIL>', 'Subject of the Email', 'This is the body of the email.');

// Send an HTML email
Mail::send('<EMAIL>', 'HTML Email Subject', '<h1>Hello!</h1><p>This is an HTML email.</p>', true); // true for HTML

// Send with attachments
Mail::send('<EMAIL>', 'Email with Attachment', 'Please see the attached file.', false, ['/path/to/your/file.pdf']);
```

---

## 15. Session (`Zen\Core\Session`)

The `Session` class provides a simple interface for managing user sessions and storing temporary data.

```php
<?php

use Zen\Core\Session;

// Set a session variable
Session::set('user_id', 123);
Session::set('username', 'john_doe');

// Get a session variable
$userId = Session::get('user_id'); // 123

// Check if a session variable exists
if (Session::has('username')) {
    echo "Username is set in session.";
}

// Remove a session variable
Session::remove('username');

// Get all session data
$allSessionData = Session::all();

// Flash data (data that will only be available for the next request)
Session::flash('status', 'Profile updated successfully!');

// Retrieve flashed data (on the next request)
$status = Session::getFlash('status'); // 'Profile updated successfully!'
```

---

## 16. Settings (`Zen\Core\Settings`)

The `Settings` class allows you to manage application-wide settings, often stored in files within `storage/secured/settings/`.

```php
<?php

use Zen\Core\Settings;

// Get a setting value
$siteName = Settings::get('site.name'); // Assuming 'site.name' is defined in your settings

// Set a setting value (usually for runtime, not persistent storage unless explicitly saved)
Settings::set('site.maintenance_mode', true);

// Check if a setting exists
if (Settings::has('site.analytics_id')) {
    $analyticsId = Settings::get('site.analytics_id');
}

// You might have methods to load/save settings from/to persistent storage
// Settings::load('site');
// Settings::save('site');
```

---

## 17. Storage (`Zen\Core\Storage`)

The `Storage` class provides utilities for interacting with the file system, including reading, writing, and deleting files.

```php
<?php

use Zen\Core\Storage;

// Write content to a file
Storage::put('path/to/file.txt', 'Hello, Zen Storage!');

// Get content from a file
$content = Storage::get('path/to/file.txt'); // "Hello, Zen Storage!"

// Check if a file exists
if (Storage::exists('path/to/file.txt')) {
    echo "File exists.";
}

// Delete a file
Storage::delete('path/to/old_file.txt');

// Get all files in a directory
$files = Storage::files('path/to/directory');

// Get all directories in a directory
$directories = Storage::directories('path/to/parent_directory');

// Create a directory
Storage::makeDirectory('path/to/new_directory');
```

---

## 18. Validate (`Zen\Core\Validate`)

The `Validate` class provides methods for data validation, ensuring that input meets specified criteria.

```php
<?php

use Zen\Core\Validate;

$data = [
    'email'    => '<EMAIL>',
    'password' => 'password123',
    'age'      => 25,
];

$rules = [
    'email'    => 'required|email',
    'password' => 'required|min:8',
    'age'      => 'required|integer|min:18',
];

$validator = new Validate($data, $rules);

if ($validator->fails()) {
    // Get validation errors
    $errors = $validator->errors();
    print_r($errors);
} else {
    echo "Validation passed!";
}

// You can also add custom validation rules
// Validate::addRule('custom_rule', function ($value) {
//     return $value === 'expected';
// }, 'The :attribute must be expected.');
```

---

## 19. Defer (`Zen\Core\Defer`)

The `Defer` class allows you to register callbacks to be executed at a later point in the application lifecycle, typically just before the script terminates. This is useful for tasks like sending emails, logging, or cleaning up resources after the main response has been sent.

```php
<?php

use Zen\Core\Defer;

// Register a deferred task
Defer::run(function () {
    // This code will execute later
    error_log('Deferred task executed!');
});

// You can register multiple deferred tasks
Defer::run(function () {
    // Another task
    error_log('Another deferred task.');
});

// The deferred tasks will automatically run when the application shuts down.
// You typically don't need to call anything explicitly to trigger them.
```

---

This documentation provides a foundational understanding of the Zen Framework. For more detailed information, explore the source code within the `core/` directory.