<?php

namespace App\Controller;

use Zen\Core\Controller;
use Zen\Core\Language;
use Zen\Core\Theme;
use Zen\Core\Cache;
use Zen\Core\Session;
use Zen\Core\Hash;
use Zen\Core\Storage;
use Zen\Core\Db;
use Zen\Core\GraphQL;
use Zen\Core\Validate;
use Zen\Core\Settings;
use Zen\Core\Plugin;
use Zen\Core\Auth;

use App\Model\User;

class Test extends Controller
{
    public function test()
    {
        echo "<pre>";

        print_r(Language::scan());

        print_r(\Zen\Core\Language::meta());
        exit;

        var_dump(r('test.index'));
        var_dump(s('mail.host'));
        var_dump(l('dashboard/websites/meta:website_description'));

        // Cache::set("Hello", "Worldy");
        var_dump(Cache::get("Hello"));

    }

    public function auth()
    {
        Auth::configure([
            'driver' => 'session', // or 'database'
            'session' => [
                'table' => 'sessions', // table name for session storage
                'lifetime' => 120, // session lifetime in minutes
            ],
            'database' => [
                'table' => 'users', // table name for user storage
                'username_column' => 'email', // column for username
                'password_column' => 'password', // column for password
            ]
        ]);
    }

    public function session()
    {
        //Session::regenerate();
        Session::set('test', 'value of test is here bro');
        Session::clear();
        Session::set('test', 'value of test is here bro');
        Session::regenerate();
        $this->view('test', [
            'test' => 'Session',
            'value' => Session::get('test')
        ]);
    }

    public function login()
    {
        // Enable session support (do this in your app initialization)
        Auth::useSessions(true);

        $credentials = [
            'email' => '<EMAIL>',
            'password' => 'password'
        ];

        // Login user (will create a session record)
        Auth::guard()->attempt($credentials, $remember = true);

        // Get all active sessions for the current user
        $sessions = Auth::guard()->getSessions();

        // Terminate a specific session
        // Auth::guard()->terminateSession($sessionToken);

        // // Logout from current session only
        // Auth::guard()->logout();

        // // Logout from all sessions
        // Auth::guard()->logout(true);
    }
    public function pagination()
    {
        $pagination = User::paginate(2)->performanceMode()->setPageParam('p', true)->fetch('*');

        // Get items for current page
        $items = $pagination->getItems();

        // Example: render items and links
        foreach ($items as $item) {
            // Render your item
            echo $item['username'] . '<br>';
        }

        // Get pagination links
        $links = $pagination->links();

        echo "<br>";

        // display using view: resources/themes/web/default/pagination.html
        echo $pagination->render('pagination');

        // display using a pattern
        echo $pagination->renderPattern([
            'container' => '<ul>:loop:</ul>',
            'item' => '<li><a href=":url:">:label:</a></li>',
            'active' => '<li class="active"><a href="javascript:void(0);">:label:</a></li>',
            'disabled' => '<li class="disabled"><span>:label:</span></li>',
            'prev' => '<li><a href=":url:">&laquo;</a></li>',
            'next' => '<li><a href=":url:">&raquo;</a></li>',
            'prev_disabled' => '<li class="disabled"><span>&laquo;</span></li>',
            'next_disabled' => '<li class="disabled"><span>&raquo;</span></li>'
        ]);

        echo "<br>";

        // display manually
        echo '<a href="' . $links['prev']['url'] . '">' . $links['prev']['label'] . '</a> ';
        // Render pagination links
        foreach ($links['links'] as $link) {
            if (isset($link['active']) && $link['active']) {
                echo '<a href="javascript:;">' . $link['label'] . '</a> ';
            } elseif ($link['url']) {
                echo '<a href="' . $link['url'] . '">' . $link['label'] . '</a> ';
            } else {
                echo '<a href="javascript:;">' . $link['label'] . '</a> ';
            }
        }
        echo '<a href="' . $links['next']['url'] . '">' . $links['next']['label'] . '</a> ';
    }

    public function index()
    {
        $this->view('test', [
            'methods' => get_class_methods($this)
        ]);
    }

    public function languages()
    {
        echo l('dashboard/websites/meta:website_description');
        echo "<br>";
        echo l('dashboard/websites/website_description');
        echo l('dashboard/websites/test->test');
        echo "<br>";
        echo l('dashboard/websites/meta:website_description', '', 'theme:web:default');
        echo "<br>";
        echo l('theme:web/dashboard/websites/website_description');
        echo "<br>";
    }

    public function mail()
    {
        // Mail test logic here
        $this->view('test', [
            'test' => 'Mail'
        ]);
    }

    public function theme()
    {
        Theme::setTheme('admin', 'default');
        $this->view('test', [
            'test' => 'Theme',
            'theme' => Theme::get()
        ]);
    }

    public function cache()
    {
        Cache::set('test', 'value');
        $this->view('test', [
            'test' => 'Cache',
            'value' => Cache::get('test')
        ]);
    }

    public function hash()
    {
        $this->view('test', [
            'test' => 'Hash',
            'hashed' => Hash::make('password')
        ]);
    }

    public function storage()
    {
        Storage::disk('public')->put('test.txt', 'test');
        $this->view('test', [
            'test' => 'Storage',
            'content' => Storage::disk('public')->get('test.txt')
        ]);
    }

    public function db()
    {
        $users = DB::select('SELECT * FROM users');
        $this->view('test', [
            'test' => 'DB',
            'users' => $users
        ]);
    }

    public function graphql()
    {
        // GraphQL test logic here
        $this->view('test', [
            'test' => 'GraphQL'
        ]);
    }

    public function validate()
    {
        $validation = Validate::run([
            'username' => 'required|min:3|max:20',
            'password' => 'required|min:6'
        ], [
            'username' => 'admin',
            'password' => 'password'
        ]);

        $this->view('test', [
            'test' => 'Validate',
            'errors' => $validation->getErrors()
        ]);
    }
}
