<?php

namespace App\Database\Migrations;

use Doctrine\DBAL\Schema\Schema;

class Hello
{
    public function up(Schema $schema): void
    {
        // Add your migration logic here
        $table = $schema->createTable('hellos');
        $table->addColumn('id', 'integer', ['unsigned' => true, 'autoincrement' => true]);
        $table->addColumn('username', 'string', ['length' => 32]);
        $table->setPrimaryKey(['id']);
    }

    public function down(Schema $schema): void
    {
        // Add your rollback logic here
        $schema->dropTable('hellos');
    }
}
