<?php

use GraphQL\Type\Definition\Type;
use Zen\Core\GraphQL;

// Get GraphQL instance
$graphql = GraphQL::getInstance();

// Example: Create a User type and CRUD operations
$userType = $graphql->createEntityType('User', 'users', [
    'id' => Type::id(),
    'name' => Type::string(),
    'email' => Type::string(),
    'created_at' => Type::string()
]);

// Example: Add a custom query
$graphql->addQueryField('userByEmail', [
    'type' => $userType,
    'args' => [
        'email' => Type::nonNull(Type::string())
    ],
    'resolve' => function($root, $args) use ($graphql) {
        $db = $graphql->getDb();
        $qb = $db->createQueryBuilder();
        return $qb->select('*')
            ->from('users')
            ->where('email = :email')
            ->setParameter('email', $args['email'])
            ->executeQuery()
            ->fetchAssociative();
    }
]);

// Example: Add a mutation
$graphql->addMutationField('createUser', [
    'type' => $userType,
    'args' => [
        'name' => Type::nonNull(Type::string()),
        'email' => Type::nonNull(Type::string())
    ],
    'resolve' => function($root, $args) use ($graphql) {
        $db = $graphql->getDb();
        $qb = $db->createQueryBuilder();
        
        $qb->insert('users')
            ->values([
                'name' => ':name',
                'email' => ':email',
                'created_at' => ':created_at'
            ])
            ->setParameters([
                'name' => $args['name'],
                'email' => $args['email'],
                'created_at' => date('Y-m-d H:i:s')
            ])
            ->executeQuery();

        $id = $db->lastInsertId();
        
        // Return the created user
        return $qb->select('*')
            ->from('users')
            ->where('id = :id')
            ->setParameter('id', $id)
            ->executeQuery()
            ->fetchAssociative();
    }
]);
