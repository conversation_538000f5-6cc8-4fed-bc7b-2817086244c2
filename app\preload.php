<?php

/**
 * OPcache Preload File
 * This file preloads commonly used classes to improve performance
 */

// Prevent direct access
if (PHP_SAPI !== 'cli') {
    die('This file can only be executed via CLI');
}

require_once __DIR__ . '/../vendor/autoload.php';

// Helper function to preload a directory
function preloadDirectory(string $dir): void {
    if (!is_dir($dir)) {
        return;
    }

    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );

    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            require_once $file->getPathname();
        }
    }
}

// Preload core framework classes
preloadDirectory(__DIR__ . '/../core/Core');

// Preload common application classes
preloadDirectory(__DIR__ . '/../app/Controller');
preloadDirectory(__DIR__ . '/../app/Model');

// Explicitly preload specific high-usage classes
require_once __DIR__ . '/../core/Core/Router.php';
require_once __DIR__ . '/../core/Core/Cli.php';

echo "Preloading completed successfully.\n";
