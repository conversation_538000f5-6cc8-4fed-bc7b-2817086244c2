<?php

use Zen\Core\Router;
Router::set('GET', '/', 'App\Controller\Frontpage::index', 'frontpage');


// Test Routes
Router::group('/test', function () {
    Router::set('GET', '/', 'App\Controller\Test::index', 'test.index');
    Router::set('GET', '/test', 'App\Controller\Test::test', 'test.test');
    Router::set('GET', '/pagination', 'App\Controller\Test::pagination', 'test.pagination');
    Router::set('GET', '/languages', 'App\Controller\Test::languages', 'test.languages');
    Router::set('GET', '/mail', 'App\Controller\Test::mail', 'test.mail');
    Router::set('GET', '/theme', 'App\Controller\Test::theme', 'test.theme');
    Router::set('GET', '/cache', 'App\Controller\Test::cache', 'test.cache');
    Router::set('GET', '/session', 'App\Controller\Test::session', 'test.session');
    Router::set('GET', '/hash', 'App\Controller\Test::hash', 'test.hash');
    Router::set('GET', '/storage', 'App\Controller\Test::storage', 'test.storage');
    Router::set('GET', '/db', 'App\Controller\Test::db', 'test.db');
    Router::set('GET', '/graphql', 'App\Controller\Test::graphql', 'test.graphql');
    Router::set('GET', '/validate', 'App\Controller\Test::validate', 'test.validate');
});

Router::set('GET', '/login', 'App\Controller\Login::index', 'loginin\_index');
