{"name": "devknown/zen", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.4 || ^8.0", "doctrine/dbal": "4.2.1", "symfony/http-foundation": "^7.2.0", "devknown/twig-import-function": "^1.0", "twig/twig": "3.16.0", "webonyx/graphql-php": "^15.19", "phpmailer/phpmailer": "^6.9"}, "require-dev": {"phpunit/phpunit": "^11.4"}, "autoload": {"psr-4": {"Zen\\": "core/", "App\\": "app/"}}}