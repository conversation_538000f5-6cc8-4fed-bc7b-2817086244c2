<?php

namespace Zen\Core;

/**
 * Class Auth
 * Handles authentication, session management, and user retrieval for different guards (user/admin).
 */
class Auth
{
    /**
     * @var array<string, self> Singleton instances for each guard.
     */
    private static array $instances = [];

    /**
     * @var bool Whether to use database sessions
     */
    private static bool $useSessions = false;

    /**
     * @var array<string, array> Configuration for each guard.
     */
    private static array $config = [
        'user' => [
            'model' => 'App\Model\User',
            'session_key' => 'auth_user_id',
            'session_model' => 'App\Model\Session',
            'session_token' => 'auth_user_token',
            'provider_model' => 'App\Model\AuthProvider'
        ],
        'admin' => [
            'model' => 'App\Model\Admin',
            'session_key' => 'auth_admin_id',
            'session_model' => 'App\Model\Session',
            'session_token' => 'auth_admin_token',
            'provider_model' => 'App\Model\AuthProvider'
        ]
    ];

    /**
     * @var array<string, int> Session expiration times for different guards.
     */
    private static array $sessionExpires = [
        'user' => 24 * 60 * 60, // 24 hours
        'admin' => 24 * 60 * 60 // 24 hours
    ];

    /**
     * @var string The current guard name.
     */
    private string $guard;
    /**
     * @var object|null The currently authenticated user object.
     */
    private ?object $authenticatable = null;
    /**
     * Auth constructor.
     * @param string $guard
     */
    private function __construct(string $guard = 'user')
    {
        $this->guard = $guard;
    }

    /**
     * Get the Auth instance for a specific guard (singleton).
     * @param string $guard
     * @return self
     */
    public static function guard(string $guard = 'user'): self
    {
        if (!isset(self::$instances[$guard])) {
            self::$instances[$guard] = new self($guard);
        }
        return self::$instances[$guard];
    }

    /**
     * Merge new configuration for guards.
     * @param array $config
     * @return void
     */
    public static function configure(array $config): void
    {
        self::$config = array_merge(self::$config, $config);
    }

    /**
     * Attempt to authenticate with credentials.
     * @param array $credentials
     * @param bool $remember
     * @return bool
     */
    public function attempt(array $credentials, bool $remember = false): bool
    {
        $user = $this->getUserByCredentials($credentials);

        if (!$user || !Hash::verify($credentials['password'], $user->password)) {
            return false;
        }

        $this->login($user, $remember);
        return true;
    }

    /**
     * Register a new user.
     * @param array $data
     * @return bool
     */
    public function register(array $data, $loginAfter = true): bool
    {
        $model = self::getModel($this->guard);
        $user = $model::create([
            'email' => $data['email'],
            'username' => $data['username'],
            'password' => Hash::make($data['password']),
            'status' => 1
        ]);

        if ($loginAfter) {
            $this->login($user);
        }

        return true;
    }
    
    /**
     * Log in a user and create a session.
     * @param object $user
     * @param bool $remember
     * @return void
     */
    public function login(object $user, bool $remember = false): void
    {
        $this->authenticatable = $user;
        Session::set(self::$config[$this->guard]['session_key'], $user->id);

        if (self::$useSessions) {
            $token = Hash::make(uniqid() . time() . $user->id);
            $expiresAt = $remember ? date('Y-m-d H:i:s', strtotime('+30 days')) : date('Y-m-d H:i:s', strtotime('+24 hours'));

            $sessionModel = self::getSessionModel($this->guard);
            $sessionModel::create([
                $this->guard . '_id' => $user->id,
                'session_token' => $token,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'expires_at' => $expiresAt,
                'status' => 'active'
            ]);

            Session::set(self::$config[$this->guard]['session_token'], $token);

            if ($remember) {
                setcookie(
                    self::$config[$this->guard]['session_token'],
                    $token,
                    strtotime('+30 days'),
                    '/',
                    '',
                    true,
                    true
                );
            }
        }
    }

    public function remember($guard = 'user', $time = 86400, $forceOnExisting = false): void
    {
        self::$sessionExpires[$guard] = $time;

        if ($forceOnExisting && self::$useSessions) {
            $token = Session::get(self::$config[$guard]['session_token']);
            if ($token) {
                setcookie(
                    self::$config[$guard]['session_token'],
                    $token,
                    time() + (self::$sessionExpires[$guard] ?? 86400),
                    '/',
                    '',
                    true,
                    true
                );
            }
        }
    }

    /**
     * Log out the current user and invalidate the session.
     * @param bool $allSessions Whether to logout from all sessions
     * @return void
     */
    public function logout(bool $allSessions = false): void
    {
        if (self::$useSessions) {
            $sessionModel = self::getSessionModel($this->guard);
            if ($allSessions) {
                // Terminate all sessions for this user
                $sessionModel::where($this->guard . '_id', $this->authenticatable->id)
                    ->update(['status' => 'terminated']);
            } else {
                // Terminate only current session
                $token = Session::get(self::$config[$this->guard]['session_token']);
                if ($token) {
                    $sessionModel::where('session_token', $token)
                        ->update(['status' => 'terminated']);
                }
            }

            Session::remove(self::$config[$this->guard]['session_token']);
            setcookie(self::$config[$this->guard]['session_token'], '', time() - 3600, '/');
        }

        Session::remove(self::$config[$this->guard]['session_key']);
        $this->authenticatable = null;
        Session::regenerate();
    }

    /**
     * Get the currently authenticated user object, or null if not authenticated.
     * @return object|null
     */
    public function user(): ?object
    {
        if ($this->authenticatable !== null) {
            return $this->authenticatable;
        }

        $userId = Session::get($this->guard . '_id');
        if (!$userId) {
            return null;
        }

        $this->authenticatable = $this->getUserById($userId);
        return $this->authenticatable;
    }

    /**
     * Check if a user is authenticated.
     * @return bool
     */
    public function check(): bool
    {
        $user = $this->user();
        if ($user === null) {
            return false;
        }

        if (self::$useSessions && !$this->validateSession()) {
            $this->logout();
            return false;
        }

        return true;
    }

    /**
     * Check if the current user is a guest (not authenticated).
     * @return bool
     */
    public function guest(): bool
    {
        return !$this->check();
    }

    /**
     * Retrieve a user by credentials (e.g., email or username ).
     * @param array $credentials
     * @return object|null
     */
    private function getUserByCredentials(array $credentials): ?object
    {
        if (!isset($credentials['email']) && !isset($credentials['username']) && !isset($credentials['identifier'])) {
            return null;
        }

        $model = self::$config[$this->guard]['model'];

        if (isset($credentials['email'])) {
            $credentials['email'] = strtolower($credentials['email']);
            return $model::where('email', $credentials['email'])
                ->where('status', 1)->first();
        } elseif (isset($credentials['username'])) {
            $credentials['username'] = strtolower($credentials['username']);
            return $model::where('username', $credentials['username'])
                ->where('status', 1)->first();
        } elseif (isset($credentials['identifier'])) {
            $credentials['identifier'] = strtolower($credentials['identifier']);
            return $model::where('username', $credentials['identifier'])
                ->orWhere('email', $credentials['identifier'])
                ->where('status', 1)->first();
        }

        return null;
    }

    /**
     * Retrieve a user by ID.
     * @param int $id
     * @return object|null
     */
    private function getUserById(int $id): ?object
    {
        $model = self::$config[$this->guard]['model'];
        return $model::find($id);
    }

    /**
     * Set the model class for a guard.
     * @param string $guard
     * @param string $model
     * @return void
     */
    public static function setModel(string $guard, string $model): void
    {
        self::$config[$guard]['model'] = $model;
    }

    /**
     * Get the model class for a guard.
     * @param string $guard
     * @return string
     */
    public static function getModel(string $guard): string
    {
        return self::$config[$guard]['model'];
    }

    /**
     * Get the session model class for a guard.
     * @param string $guard
     * @return string
     */
    public static function getSessionModel(string $guard): string
    {
        return self::$config[$guard]['session_model'];
    }

    /**
     * Enable or disable database session support
     * @param bool $enable
     * @return void
     */
    public static function useSessions(bool $enable = true): void
    {
        self::$useSessions = $enable;
    }

    /**
     * Get all active sessions for the current user
     * @return array
     */
    public function getSessions(): array
    {
        if (!self::$useSessions || !$this->check()) {
            return [];
        }

        $sessionModel = self::getSessionModel($this->guard);
        return $sessionModel::where($this->guard . '_id', $this->authenticatable->id)
            ->where('status', 'active')
            ->get()
            ->toArray();
    }

    /**
     * Terminate a specific session by its token
     * @param string $sessionToken
     * @return bool
     */
    public function terminateSession(string | array $sessionToken): bool
    {
        if (!self::$useSessions || !$this->check()) {
            return false;
        }

        if (is_array($sessionToken)) {
            foreach($sessionToken as $token) {
                $this->terminateSession($token);
            }
            return true;
        }

        $sessionModel = self::getSessionModel($this->guard);
        return $sessionModel::where('session_token', $sessionToken)
            ->where($this->guard . '_id', $this->authenticatable->id)
            ->update(['status' => 'terminated']) > 0;
    }

    /**
     * Check if the current session is valid
     * @return bool
     */
    private function validateSession(): bool
    {
        if (!self::$useSessions) {
            return true;
        }

        $token = Session::get(self::$config[$this->guard]['session_token']);
        if (!$token) {
            return false;
        }

        $sessionModel = self::getSessionModel($this->guard);
        $session = $sessionModel::where('session_token', $token)
            ->where('status', 'active')
            ->where('expires_at', '>', date('Y-m-d H:i:s'))
            ->first();

        return $session !== null;
    }

    /**
     * Get the provider model class for a guard
     * @param string $guard
     * @return string
     */
    public static function getProviderModel(string $guard): string
    {
        return self::$config[$guard]['provider_model'];
    }

    /**
     * Attempt to authenticate or create a user with third-party provider data
     * @param string $provider Provider name (e.g., 'google', 'github')
     * @param array $providerData Provider user data
     * @param bool $autoCreate Create user if not exists
     * @return bool
     */
    public function attemptProvider(string $provider, array $providerData, bool $autoCreate = true): bool
    {
        $providerModel = self::getProviderModel($this->guard);
        $providerUser = $providerModel::where('provider', $provider)
            ->where('provider_user_id', $providerData['id'])
            ->first();

        if ($providerUser) {
            // Provider account exists, log in the associated user
            $user = $this->getUserById($providerUser->user_id);
            if ($user) {
                $this->login($user);
                return true;
            }
            return false;
        }

        if (!$autoCreate) {
            return false;
        }

        // Create new user and provider account
        $model = self::getModel($this->guard);
        $user = $model::create([
            'email' => $providerData['email'],
            'username' => $this->generateUsername($providerData),
            'password' => Hash::make(uniqid()), // Random password for provider-created accounts
            'status' => 1
        ]);

        $this->linkProvider($user, $provider, $providerData);
        $this->login($user);

        return true;
    }

    /**
     * Link a third-party provider account to an existing user
     * @param object $user User model instance
     * @param string $provider Provider name
     * @param array $providerData Provider user data
     * @return bool
     */
    public function linkProvider(object $user, string $provider, array $providerData): bool
    {
        $providerModel = self::getProviderModel($this->guard);

        // Check if provider account is already linked to another user
        $existingLink = $providerModel::where('provider', $provider)
            ->where('provider_user_id', $providerData['id'])
            ->first();

        if ($existingLink) {
            return false;
        }

        // Create new provider link
        $providerModel::create([
            'user_id' => $user->id,
            'provider' => $provider,
            'provider_user_id' => $providerData['id'],
            'provider_email' => $providerData['email'] ?? null,
            'provider_data' => json_encode($providerData)
        ]);

        return true;
    }

    /**
     * Unlink a third-party provider from the user's account
     * @param string $provider Provider name
     * @return bool
     */
    public function unlinkProvider(string $provider): bool
    {
        if (!$this->check()) {
            return false;
        }

        $providerModel = self::getProviderModel($this->guard);
        return $providerModel::where('provider', $provider)
            ->where('user_id', $this->authenticatable->id)
            ->delete() > 0;
    }

    /**
     * Get all linked providers for the current user
     * @return array
     */
    public function getLinkedProviders(): array
    {
        if (!$this->check()) {
            return [];
        }

        $providerModel = self::getProviderModel($this->guard);
        return $providerModel::where('user_id', $this->authenticatable->id)
            ->get()
            ->toArray();
    }

    /**
     * Generate a unique username from provider data
     * @param array $providerData
     * @return string
     */
    private function generateUsername(array $providerData): string
    {
        $base = strtolower(preg_replace(
            '/[^a-zA-Z0-9]/',
            '',
            $providerData['name'] ??
            explode('@', $providerData['email'])[0] ??
            'user'
        ));

        $model = self::getModel($this->guard);
        $username = $base;
        $i = 1;

        while ($model::where('username', $username)->exists()) {
            $username = $base . $i++;
        }

        return $username;
    }
}
