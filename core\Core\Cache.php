<?php

namespace Zen\Core;

/**
 * Class Cache
 * Provides a simple file-based caching system.
 */
class Cache
{
    /**
     * @var string The directory where cache files are stored.
     */
    protected static $cachePath = ZEN_PATH . '/storage/secured/cache/data/';

    /**
     * Initialize the cache directory.
     */
    public static function init()
    {
        if (!is_dir(self::$cachePath)) {
            mkdir(self::$cachePath, 0777, true);
        }
    }

    /**
     * Get the full path for a cache key.
     * @param string $key
     * @return string
     */
    protected static function getCacheFile(string $key): string
    {
        return self::$cachePath . md5($key) . '.cache';
    }

    /**
     * Store an item in the cache.
     * @param string $key
     * @param mixed $value
     * @param int $minutes
     * @return void
     */
    public static function set(string $key, $value, int $minutes = 60): void
    {
        self::init();
        $file = self::getCacheFile($key);
        $expiration = time() + ($minutes * 60);
        $data = serialize(['expires' => $expiration, 'data' => $value]);
        file_put_contents($file, $data);
    }

    /**
     * Retrieve an item from the cache.
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        $file = self::getCacheFile($key);

        if (!file_exists($file)) {
            return $default;
        }

        $data = unserialize(file_get_contents($file));

        if (time() > $data['expires']) {
            self::forget($key);
            return $default;
        }

        return $data['data'];
    }

    /**
     * Check if an item exists in the cache.
     * @param string $key
     * @return bool
     */
    public static function has(string $key): bool
    {
        return self::get($key) !== null;
    }

    /**
     * Remove an item from the cache.
     * @param string $key
     * @return bool
     */
    public static function forget(string $key): bool
    {
        $file = self::getCacheFile($key);

        if (file_exists($file)) {
            return unlink($file);
        }

        return false;
    }

    /**
     * Clear the entire cache.
     * @return void
     */
    public static function flush(): void
    {
        self::init();
        $files = glob(self::$cachePath . '*.cache');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }

    /**
     * Get an item from the cache, or execute the given Closure and store the result.
     * @param string $key
     * @param int $minutes
     * @param \Closure $callback
     * @return mixed
     */
    public static function remember(string $key, int $minutes, \Closure $callback)
    {
        if (($value = self::get($key)) !== null) {
            return $value;
        }

        $value = $callback();

        self::set($key, $value, $minutes);

        return $value;
    }
}