<?php

namespace Zen\Core;

use Zen\Core\File;

/**
 * Class Cli
 * Handles command-line interface (CLI) operations for the Zen Framework.
 * Provides functionalities for running development server, creating files (controllers, models, layers),
 * listing routes and layers, and setting up authentication.
 */
class Cli
{
    private static array $arguments = [];
    private static array $commands = [];
    private static string $currentCommand = '';

    public static function run()
    {
        global $argv;

        if (!isset($argv[1])) {
            CliOutput::showIntro();
            self::showHelp();
            return;
        }

        self::parseArguments();
        self::executeCommand();
    }

    private static function parseArguments()
    {
        global $argv;

        self::$currentCommand = $argv[1] ?? '';

        for ($i = 2; $i < count($argv); $i++) {
            $arg = $argv[$i];

            if (strpos($arg, '--') === 0) {
                // Handle --key=value format
                if (strpos($arg, '=') !== false) {
                    list($key, $value) = explode('=', substr($arg, 2), 2);
                    self::$arguments[$key] = $value;
                }
                // Handle --key value format
                else {
                    $key = substr($arg, 2);
                    $value = $argv[$i + 1] ?? true;

                    if (is_string($value) && strpos($value, '--') === 0) {
                        $value = true;
                    } else {
                        $i++;
                    }

                    self::$arguments[$key] = $value;
                }
            }
        }
    }

    private static function showHelp()
    {
        CliOutput::section('Available Commands');
        CliOutput::table(
            ['Command', 'Description'],
            [
                ['serve', 'Start the development server'],
                ['make:controller', 'Create a new controller'],
                ['make:model', 'Create a new model'],
                ['make:layer', 'Create a new middleware layer'],
                ['make:migration', 'Create a new database migration'],
                ['list:routes', 'List all routes'],
                ['list:layers', 'List all layers / middlewares'],
                ['setup:auth', 'Set up authentication system with controllers and middleware'],
                ['reset', 'Reset the application to initial state'],
                ['migrate', 'Run database migrations'],
                ['migrate:rollback', 'Rollback the last database migration'],
            ]
        );

        CliOutput::section('Usage Examples');
        CliOutput::list([
            'php zen serve --port=8000',
            'php zen make:controller --name=User',
            'php zen make:model --name=Product',
            'php zen make:layer --name=Auth',
            'php zen make:migration --name=CreateUsersTable',
            'php zen setup:auth',
            'php zen migrate'
        ], '→');

        echo "\n";
    }

    private static function executeCommand()
    {
        switch (self::$currentCommand) {
            case 'serve':
                self::serveApplication();
                break;
            case 'make:controller':
                self::makeController();
                break;
            case 'make:model':
                self::makeModel();
                break;
            case 'make:layer':
                self::makeLayer();
                break;
            case 'make:migration':
                self::makeMigration();
                break;
            case 'list:routes':
                self::listRoutes();
                break;
            case 'list:layers':
                self::listLayers();
                break;
            case 'setup:auth':
                self::setupAuth();
                break;
            case 'reset':
                self::resetProject();
                break;
            case 'migrate':
                self::runMigrations();
                break;
            case 'migrate:rollback':
                self::runRollback();
                break;
            default:
                self::showHelp();
                break;
        }
    }

    private static function serveApplication()
    {
        $port = self::$arguments['port'] ?? 8000;
        $host = self::$arguments['host'] ?? '127.0.0.1';
        $projectPath = ZEN_PATH;

        CliOutput::header('Starting Development Server');
        CliOutput::info("Server URL: http://{$host}:{$port}");
        CliOutput::info("Document Root: {$projectPath}");
        CliOutput::warning("Press Ctrl+C to stop the server");
        echo "\n";

        shell_exec("php -S {$host}:{$port} {$projectPath}/index.php");
    }

    private static function isCallable($string)
    {
        // Trim and split the string by "::"
        $parts = explode('::', trim($string));

        if (count($parts) !== 2) {
            return false; // Invalid format
        }

        [$class, $method] = $parts;

        if (!class_exists($class)) {
            return false; // Class does not exist
        }

        if (!method_exists($class, $method)) {
            return false; // Method does not exist in the class
        }

        return true;
    }

    private static function listRoutes()
    {
        CliOutput::section('Routes:');

        $routes = [];
        foreach (\Zen\Core\Router::list() as $key => $route) {
            if(is_array($route[2])) {
                $route[2] = array_slice($route[2], 0, 2);
                $route[2] = implode('::', $route[2]);
            }
            $routes[$key] = [
                self::isCallable($route[2]) ? 'Callable' : 'Missing  ',
                $route[0],
                $route[1],
                $route[2],
                $route[4]
            ];
        }

        CliOutput::table(
            ['Status', 'Method', 'Path', 'Controller', 'Name'],
            $routes
        );
    }

    private static function listLayers()
    {
        CliOutput::section('Layers:');

        $layers = [];
        $routeLayers = [];
        $anonymousLayers = [];
        $registeredLayers = [];

        // First, get all registered layers from Layer class
        try {
            $registeredLayers = \Zen\Core\Layer::list();
        } catch(\Exception $e) {
            CliOutput::error("Error getting registered layers: " . $e->getMessage());
        }

        // Process layers attached to routes
        try {
            foreach (\Zen\Core\Router::list() as $routeName => $route) {
                if(!empty($route[3])) {
                    $layerCount = 0;
                    foreach($route[3] as $layer) {
                        $layerKey = '';
                        $layerStatus = "Missing";

                        // Handle anonymous function layers
                        if(is_callable($layer) && !is_string($layer)) {
                            $layerKey = Layer::generateLayerName($routeName, $layerCount);
                            $layerStatus = isset($registeredLayers[$layerKey]) ? "Exists" : "Inline";
                        }
                        // Handle named layers
                        elseif(is_string($layer)) {
                            $layerKey = $layer;
                            $layerStatus = isset($registeredLayers[$layerKey]) ? "Exists" : "Missing";
                        }

                        if(!empty($layerKey)) {
                            $routeLayers[$layerKey][] = [
                                $layerStatus,
                                $layerKey,
                                $route[1] ?? 'n/a', // Route path
                                $route[4] ?? 'n/a'  // Route name
                            ];
                        }

                        $layerCount++;
                    }
                }
            }
        } catch(\Exception $e) {
            CliOutput::error("Error processing route layers: " . $e->getMessage());
        }

        // Find standalone layers (not attached to any route)
        try {
            foreach ($registeredLayers as $layerName => $callback) {
                if(!isset($routeLayers[$layerName])) {
                    $anonymousLayers[] = [
                        "Unused ",
                        $layerName,
                        "n/a",
                        "n/a"
                    ];
                }
            }

            // Process route layers
            foreach ($routeLayers as $layerRoutes) {
                foreach($layerRoutes as $routeLayer) {
                    $layers[] = $routeLayer;
                }
            }
        } catch(\Exception $e) {
            CliOutput::error("Error processing standalone layers: " . $e->getMessage());
        }

        // Combine all layers
        $layers = array_merge($layers, $anonymousLayers);

        if (empty($layers)) {
            CliOutput::info("No layers found in the application.");
            return;
        }

        CliOutput::table(
            ['Status', 'Layer', 'Path', 'Route'],
            $layers
        );
    }

    private static function makeController()
    {
        CliOutput::header('Creating New Controller');

        $name = self::$arguments['name'] ?? CliOutput::prompt("Enter controller name");
        $name = ucfirst($name);
        $path = ZEN_PATH . "/app/Controller/{$name}.php";

        if(is_file($path)) {
            CliOutput::error("\n✗ Error: Controller {$name} already exists. Please choose a different name.");
            return;
        }

        CliOutput::section('Configure Methods');
        CliOutput::info("Common methods: index, create, store, edit, update, delete");
        $methods = CliOutput::prompt("Enter method names (separated by comma), or press enter for default", false) ?: 'index';
        $methods = array_map('trim', explode(',', $methods));

        // Generate controller content
        $methodsCode = '';
        foreach ($methods as $method) {
            $methodsCode .= "\n    public function {$method}()\n    {\n        // Add your code here\n    }\n";
        }

        $template = "<?php\n\nnamespace App\\Controller;\n\nuse Zen\\Core\\Controller;\n\nclass {$name} extends Controller\n{{$methodsCode}}\n";

        if (!is_dir(ZEN_PATH . '/app/Controller')) {
            mkdir(ZEN_PATH . '/app/Controller', 0777, true);
        }

        if (self::writePhpFile($path, $template)) {
            CliOutput::success("\n✓ Controller {$name} created successfully!");

            if (CliOutput::confirm("\nWould you like to add routes for this controller?")) {
                self::addRoutes($name, $methods);
            }
        } else {
            CliOutput::error("\n✗ Error: Could not create controller.");
        }
    }

    private static function addRoutes($controllerName, $methods)
    {
        CliOutput::section('Adding Routes');

        $routerPath = ZEN_PATH . "/app/router.php";
        if (!file_exists($routerPath)) {
            CliOutput::error("✗ Error: Router file not found.");
            return;
        }

        $baseRoute = CliOutput::prompt("Enter base route (e.g., /users)");
        $baseRoute = trim($baseRoute, '/');

        if (\Zen\Core\Router::exists($baseRoute)) {
            CliOutput::error("✗ Error: Route '{$baseRoute}' already exists.");
            $baseRoute = CliOutput::prompt("Enter base route (e.g., /users)");
            $baseRoute = trim($baseRoute, '/');
        }

        $useGroup = CliOutput::confirm("Group these routes under '{$baseRoute}'?");

        // Handle layers
        $layers = [];
        if (CliOutput::confirm("Add layers (middleware) to these routes?")) {
            $layersInput = CliOutput::prompt("Enter layer names (separated by comma)", false);
            if ($layersInput) {
                $layers = array_map('trim', explode(',', $layersInput));
                foreach ($layers as $layer) {
                    if (!self::layerExists($layer) && CliOutput::confirm("Layer '{$layer}' doesn't exist. Create it?")) {
                        self::createLayer($layer);
                    }
                }
            }
        }

        $routeContent = "";
        if ($useGroup) {
            $routeContent .= "\nRouter::group('/{$baseRoute}', function () {";
        }

        // Add routes
        $routeCount = 0;
        foreach ($methods as $method) {
            $routePath = $useGroup ? "" : "/{$baseRoute}";
            $routeName = strtolower("{$baseRoute}_{$method}");

            switch ($method) {
                case 'index':
                    $routePath .= "";
                    break;
                case 'create':
                    $routePath .= "/create";
                    break;
                case 'store':
                    $routePath .= "/store";
                    break;
                case 'edit':
                    $routePath .= "/edit/[i:id]";
                    break;
                case 'update':
                    $routePath .= "/update/[i:id]";
                    break;
                case 'delete':
                    $routePath .= "/delete/[i:id]";
                    break;
                default:
                    if (CliOutput::confirm("Add custom route for {$method} method?")) {
                        $customPath = CliOutput::prompt("Enter route path (e.g., /custom/[i:id])");
                        $routePath .= $customPath;
                    }
            }

            $httpMethod = in_array($method, ['store', 'update', 'delete']) ? 'POST' : 'GET';
            $controllerPath = "App\\Controller\\{$controllerName}::{$method}";

            if ($useGroup) {
                $routeContent .= "\n    ";
            } else {
                $routeContent .= "\n";
            }
            $routeContent .= "Router::set('{$httpMethod}', '{$routePath}', '{$controllerPath}', '{$routeName}'";

            if (!empty($layers)) {
                $routeContent .= ", ['" . implode("', '", $layers) . "']";
            }

            $routeContent .= ");";
            $routeCount++;
        }

        if ($useGroup) {
            $routeContent .= "\n});";
        }

        // Read current router content
        $currentContent = self::readPhpFile($routerPath);
        $newContent = rtrim($currentContent, "\n?>") . $routeContent . "\n?>";

        if (self::writePhpFile($routerPath, $newContent)) {
            CliOutput::success("\n✓ Added {$routeCount} routes successfully!");
        } else {
            CliOutput::error("\n✗ Error: Could not update router file.");
        }
    }

    private static function makeModel()
    {
        CliOutput::header('Creating New Model');
        $name = self::$arguments['name'] ?? CliOutput::prompt("Enter model name");
        $name = ucfirst($name);

        $path = ZEN_PATH . "/app/Model/{$name}.php";

        if(is_file($path)) {
            CliOutput::error("\n✗ Error: Model {$name} already exists. Please choose a different name.");
            return;
        }

        CliOutput::section('Configure Table');
        $table = CliOutput::prompt("Enter table name (press enter to use lowercase plural form)", false);
        if (empty($table)) {
            $table = strtolower($name) . 's';
        }

        CliOutput::section('Configure Fillable Fields');
        $fillable = CliOutput::prompt("Enter fillable fields (separated by comma), or press enter to skip", false);
        $fillableArray = $fillable ? array_map('trim', explode(',', $fillable)) : [];

        $fillableCode = empty($fillableArray) ? '' : "\n    protected \$fillable = ['" . implode("', '", $fillableArray) . "'];";

        $template = "<?php\n\nnamespace App\\Model;\n\nuse Zen\\Core\\Model;\n\nclass {$name} extends Model\n{\n    protected \$table = '{$table}';{$fillableCode}\n    \n    // Add your properties and methods here\n}\n";


        CliOutput::section('Make migration');
        $migration = CliOutput::confirm("Would you like to create migration");
        if ($migration) {
            self::makeMigration();
        }


        if (!is_dir(ZEN_PATH . '/app/Model')) {
            mkdir(ZEN_PATH . '/app/Model', 0777, true);
        }

        if (self::writePhpFile($path, $template)) {
            CliOutput::success("\n✓ Model {$name} created successfully!");
        } else {
            CliOutput::error("\n✗ Error: Could not create model.");
        }
    }

    private static function makeLayer()
    {
        CliOutput::header('Creating New Layer');
        $name = self::$arguments['name'] ?? CliOutput::prompt("Enter layer name");
        self::createLayer($name);
    }

    private static function readPhpFile($filePath)
    {
        $content = file_get_contents($filePath);
        if (substr(rtrim($content, ' '), -2) !== '?>') {
            $content .= '?>';
        }
        return $content;
    }

    private static function writePhpFile($filePath, $content)
    {
        if (substr(rtrim($content, ' '), -2) === '?>') {
            $content = substr($content, 0, -2);
        }
        return file_put_contents($filePath, $content);
    }

    private static function layerExists($layer)
    {
        return Layer::exists($layer);
    }

    private static function createLayer($layerName, $extra = null)
    {
        $layersPath = ZEN_PATH . "/app/layers.php";
        if (!file_exists($layersPath)) {
            echo "Error: layers.php file not found.\n";
            return;
        }

        if(is_null($extra)) {
            $layerContent = "\nLayer::set('{$layerName}', function () {\n    \$checkpoint = true;\n    if (!\$checkpoint) {\n        Error::print(\"Authentication required for {$layerName}\", false);\n        return false;\n    }\n    return true;\n});\n";
        } else {
            $layerContent = "\nLayer::set('{$layerName}', function () {\n    {$extra}\n});\n";
        }

        $content = self::readPhpFile($layersPath);
        $content = str_replace("?>", "{$layerContent}?>", $content);

        if (self::writePhpFile($layersPath, $content)) {
            CliOutput::success("\n✓ Layer {$layerName} created successfully!");
        } else {
            CliOutput::error("\n✗ Error: Could not create layer.");
        }
    }

    private static function setupAuth()
    {
        CliOutput::header('Setting up Authentication System');

        // Get configuration from user
        $useSessions = CliOutput::confirm("Would you like to use session-based authentication?");
        $useCustomTables = CliOutput::confirm("Would you like to use custom table names?");

        $config = [
            'tables' => [
                'users' => 'users',
                'admins' => 'admins',
                'sessions' => 'sessions',
                'social_providers' => 'social_providers'
            ]
        ];

        if ($useCustomTables) {
            $config['tables']['users'] = CliOutput::prompt("Enter users table name", false) ?: 'users';
            $config['tables']['admins'] = CliOutput::prompt("Enter admins table name", false) ?: 'admins';
            if ($useSessions) {
                $config['tables']['sessions'] = CliOutput::prompt("Enter sessions table name", false) ?: 'sessions';
            }
            $config['tables']['social_providers'] = CliOutput::prompt("Enter social providers table name", false) ?: 'social_providers';
        }

        // 1. Create necessary directories
        $directories = [
            'app/Controller/Auth',
            'app/Model',
            'app/Layer',
            'storage/secured/setup/db',
            'storage/secured/setup/auth/controllers',
            'storage/secured/setup/auth/models',
            'storage/secured/setup/auth/layers'
        ];

        foreach ($directories as $dir) {
            $path = ZEN_PATH . "/{$dir}";
            if (!File::exists($path)) {
                File::makeDir($path);
                CliOutput::info("Created directory: {$dir}");
            }
        }

        // 2. Create Auth Controllers
        $controllers = [
            'Login' => ['login', 'authenticate', 'logout'],
            'Register' => ['register', 'store'],
            'Password' => ['forgot', 'reset', 'update'],
            'Profile' => ['show', 'edit', 'update']
        ];

        foreach ($controllers as $controller => $methods) {
            $path = ZEN_PATH . "/app/Controller/Auth/{$controller}.php";
            if (!file_exists($path)) {
                self::createAuthController($controller, $methods, $useSessions);
                CliOutput::info("Created controller: Auth/{$controller}");
            }
        }

        // 3. Create Auth Models
        $models = ['User', 'Admin'];
        if ($useSessions) {
            $models[] = 'Session';
        }

        foreach ($models as $model) {
            $path = ZEN_PATH . "/app/Model/{$model}.php";
            if (!file_exists($path)) {
                self::createAuthModel($model, $config['tables'][strtolower($model) . 's']);
                CliOutput::info("Created model: {$model}");
            }
        }

        // 4. Create Auth Middleware
        $middlewares = ['Auth', 'Guest'];
        foreach ($middlewares as $middleware) {
            if (!self::layerExists($middleware)) {
                self::createAuthLayer($middleware, $useSessions);
                CliOutput::info("Created middleware: {$middleware}");
            }
        }

        // 5. Create Auth Routes
        self::createAuthRoutes($useSessions);
        CliOutput::info("Added authentication routes");

        // 6. Copy appropriate SQL file and import if database is connected
        $sqlPath = ZEN_PATH . "/storage/secured/setup/db/auth.sql";
        $sourceSqlFile = $useSessions ?
            ZEN_PATH . "/storage/secured/setup/db/auth_with_session.sql" :
            ZEN_PATH . "/storage/secured/setup/db/auth_no_session.sql";

        if (file_exists($sourceSqlFile)) {
            $sqlContent = file_get_contents($sourceSqlFile);

            // Replace table names if custom names were provided
            foreach ($config['tables'] as $default => $custom) {
                $sqlContent = str_replace(
                    "CREATE TABLE {$default}",
                    "CREATE TABLE {$custom}",
                    $sqlContent
                );
            }

            self::writePhpFile($sqlPath, $sqlContent);
            CliOutput::info("Created auth.sql with database schema");

            // Try to connect to database and import SQL using Doctrine DBAL
            try {
                $conn = \Zen\Core\Db::dbal();

                if (!$conn->isConnected()) {
                    $conn->getNativeConnection();
                }

                if ($conn->isConnected()) {
                    CliOutput::info("Database connection successful, importing schema...");

                    // Split SQL into individual statements
                    $statements = array_filter(
                        array_map(
                            'trim',
                            explode(';', $sqlContent)
                        )
                    );

                    $success = true;
                    foreach ($statements as $statement) {
                        if (!empty($statement)) {
                            try {
                                $conn->executeStatement($statement);
                            } catch (\Exception $e) {
                                $success = false;
                                CliOutput::error("Failed to execute SQL: " . $e->getMessage());
                                break;
                            }
                        }
                    }

                    if ($success) {
                        CliOutput::success("Database schema imported successfully!");
                    }
                } else {
                    CliOutput::warning("\nDatabase connection not available.");
                    CliOutput::info("Next steps:");
                    CliOutput::list([
                        "1. Configure your database connection in app/config.php",
                        "2. Import the SQL schema from 'storage/secured/setup/db/auth.sql'",
                        "3. Test the authentication system by visiting /auth/login or /auth/register"
                    ], "→");
                }
            } catch (\Exception $e) {
                CliOutput::warning("\nDatabase connection failed: " . $e->getMessage());
                CliOutput::info("Next steps:");
                CliOutput::list([
                    "1. Configure your database connection in app/config.php",
                    "2. Import the SQL schema from 'storage/secured/setup/db/auth.sql'",
                    "3. Test the authentication system by visiting /auth/login or /auth/register"
                ], "→");
            }
        }

        // 7. Update Auth configuration
        self::updateAuthConfig($config, $useSessions);

        CliOutput::success("\n✓ Authentication system setup complete!");
    }

    private static function createAuthController($name, $methods, $useSessions)
    {
        $template = File::read(ZEN_PATH . "/storage/secured/setup/auth/controllers/{$name}.php");
        $path = ZEN_PATH . "/app/Controller/Auth/{$name}.php";
        self::writePhpFile($path, $template);
    }

    private static function createAuthModel($name, $tableName)
    {
        $template = File::read(ZEN_PATH . "/storage/secured/setup/auth/models/{$name}.php");
        $template = str_replace("{{table}}", $tableName, $template);
        $path = ZEN_PATH . "/app/Model/{$name}.php";
        self::writePhpFile($path, $template);
    }

    private static function createAuthLayer($name, $useSessions)
    {
        $template = File::read(ZEN_PATH . "/storage/secured/setup/auth/layers/{$name}.php");
        self::createLayer($name, $template);
    }

    private static function updateAuthConfig($config, $useSessions)
    {
        $configPath = ZEN_PATH . "/app/auth.php";
        if (!is_dir(dirname($configPath))) {
            mkdir(dirname($configPath), 0777, true);
        }

        $content = "<?php\n\nreturn [\n";
        $content .= "    'use_sessions' => " . ($useSessions ? 'true' : 'false') . ",\n";
        $content .= "    'tables' => [\n";
        foreach ($config['tables'] as $key => $value) {
            if (!$useSessions && $key === 'sessions') {
                continue;
            }
            $content .= "        '{$key}' => '{$value}',\n";
        }
        $content .= "    ],\n";
        $content .= "];\n";

        self::writePhpFile($configPath, $content);
    }

    private static function createAuthRoutes($useSessions)
    {
        $routesPath = ZEN_PATH . "/app/router.php";
        if (!file_exists($routesPath)) {
            CliOutput::error("Router file not found at {$routesPath}");
            return;
        }

        $routeContent = "\n\n// Authentication Routes\n";
        $routeContent .= "Router::group('/auth', function () {\n";

        // Authentication routes
        $authRoutes = [
            ['GET', '/login', 'App\Controller\Auth\Login::login', 'auth.login', ['Guest']],
            ['POST', '/login', 'App\Controller\Auth\Login::authenticate', 'auth.authenticate', ['Guest']],
            ['POST', '/logout', 'App\Controller\Auth\Login::logout', 'auth.logout', ['Auth']],
            ['GET', '/register', 'App\Controller\Auth\Register::register', 'auth.register', ['Guest']],
            ['POST', '/register', 'App\Controller\Auth\Register::store', 'auth.register.store', ['Guest']],
            ['GET', '/password/forgot', 'App\Controller\Auth\Password::forgot', 'auth.password.forgot', ['Guest']],
            ['POST', '/password/reset', 'App\Controller\Auth\Password::reset', 'auth.password.reset', ['Guest']]
        ];

        foreach ($authRoutes as $route) {
            if (!\Zen\Core\Router::exists($route[3])) {
                $formatedLayers = self::formatLayers($route[4]);
                $routeContent .= "    Router::set('{$route[0]}', '{$route[1]}', '{$route[2]}', '{$route[3]}', {$formatedLayers});\n";
            }
        }

        $routeContent .= "});\n";

        // Session Management Routes
        if ($useSessions) {
            $routeContent .= "\n// Session Management Routes\n";
            $routeContent .= "Router::group('/auth/session', function () {\n";

            $sessionRoutes = [
                ['GET', '/list', 'App\Controller\Auth\Session::list', 'auth.session.list', ['Auth']],
                ['POST', '/terminate', 'App\Controller\Auth\Session::terminate', 'auth.session.terminate', ['Auth']]
            ];

            foreach ($sessionRoutes as $route) {
                if (!\Zen\Core\Router::exists($route[3])) {
                    $formatedLayers = self::formatLayers($route[4]);
                    $routeContent .= "    Router::set('{$route[0]}', '{$route[1]}', '{$route[2]}', '{$route[3]}', {$formatedLayers});\n";
                }
            }

            $routeContent .= "});\n";
        }

        // Protected Routes
        $routeContent .= "\n// Protected Routes\n";
        $routeContent .= "Router::group('/user', function () {\n";

        $protectedRoutes = [
            ['GET', '/profile', 'App\Controller\Auth\Profile::show', 'user.profile', ['Auth']],
            ['GET', '/profile/edit', 'App\Controller\Auth\Profile::edit', 'user.profile.edit', ['Auth']],
            ['POST', '/profile/update', 'App\Controller\Auth\Profile::update', 'user.profile.update', ['Auth']]
        ];

        foreach ($protectedRoutes as $route) {
            if (!\Zen\Core\Router::exists($route[3])) {
                $formatedLayers = self::formatLayers($route[4]);
                $routeContent .= "    Router::set('{$route[0]}', '{$route[1]}', '{$route[2]}', '{$route[3]}', {$formatedLayers});\n";
            }
        }

        $routeContent .= "});\n";

        $content = self::readPhpFile($routesPath);
        $content = str_replace("?>", "{$routeContent}?>", $content);
        self::writePhpFile($routesPath, $content);
    }

    private static function formatLayers($layers)
    {
        return '[' . implode(', ', array_map(function ($layer) {
            return "'$layer'";
        }, $layers)) . ']';
    }

    private static function resetProject()
    {
        CliOutput::header('Resetting Project');

        if (!CliOutput::confirm("Are you sure you want to reset the project? This action cannot be undone.")) {
            CliOutput::info("Project reset cancelled.");
            return;
        }

        // 1. Delete controllers directory
        $controllerDir = ZEN_PATH . '/app/Controller';
        if (File::exists($controllerDir)) {
            File::deleteDir($controllerDir);
            CliOutput::info("Deleted controllers directory.");
        }
        File::makeDir($controllerDir);

        // 2. Delete models directory
        $modelDir = ZEN_PATH . '/app/Model';
        if (File::exists($modelDir)) {
            File::deleteDir($modelDir);
            CliOutput::info("Deleted models directory.");
        }
        File::makeDir($modelDir);

        // 3. Clear layers file
        $layersPath = ZEN_PATH . '/app/layers.php';
        self::writePhpFile($layersPath, "<?php

use Zen\Core\Layer;
use Zen\Core\Error;
");
        CliOutput::info("Cleared layers file.");

        // 4. Clear router file
        $routerPath = ZEN_PATH . '/app/router.php';
        self::writePhpFile($routerPath, "<?php

use Zen\Core\Router;
");
        CliOutput::info("Cleared router file.");

        // 5. Create default controller
        $controllerTemplate = "<?php

namespace App\Controller;

use Zen\Core\Controller;

class Frontpage extends Controller
{
    public function index()
    {
        // Welcome to Zen
    }
}
";
        self::writePhpFile($controllerDir . '/Frontpage.php', $controllerTemplate);
        CliOutput::info("Created Frontpage controller.");

        // 6. Create default route
        $routeContent = "
Router::set('GET', '/', 'App\Controller\Frontpage::index', 'frontpage');
";
        $currentContent = self::readPhpFile($routerPath);
        $newContent = rtrim($currentContent, "
?>") . $routeContent . "
?>";
        self::writePhpFile($routerPath, $newContent);
        CliOutput::info("Created default route.");

        CliOutput::success("
✓ Project has been reset successfully!");
    }

    private static function runMigrations()
    {
        CliOutput::header('Running Migrations');
        $migration = new \Zen\Core\DbMigrate();
        $migration->run();
    }

    private static function runRollback()
    {
        CliOutput::header('Rolling Back Migrations');
        $steps = self::$arguments['steps'] ?? 1;
        $migration = new \Zen\Core\DbMigrate();
        $migration->rollback($steps);
    }

    // TODO .. make the migration more advanced to create table etc ... 
    private static function makeMigration($params = [])
    {
        CliOutput::header('Creating New Migration');
        $name = self::$arguments['name'] ?? CliOutput::prompt("Enter migration name");
        $name = ucfirst($name);

        $path = ZEN_PATH . "/app/Database/migrations/" . date('Y_m_d_His') . "_{$name}.php";

        if(is_file($path)) {
            CliOutput::error("\n✗ Error: Migration {$name} already exists. Please choose a different name.");
            return;
        }

        $template = "<?php\n\nnamespace App\Database\Migrations;\n\nuse Doctrine\DBAL\Schema\Schema;\n\nclass {$name}
{
    public function up(Schema \$schema): void
    {
        // Add your migration logic here
        // \$table = \$schema->createTable('users');
        // \$table->addColumn('id', 'integer', ['unsigned' => true, 'autoincrement' => true]);
        // \$table->addColumn('username', 'string', ['length' => 32]);
        // \$table->setPrimaryKey(['id']);
    }

    public function down(Schema \$schema): void
    {
        // Add your rollback logic here
        // \$schema->dropTable('users');
    }
}
";

        if (!is_dir(ZEN_PATH . '/app/Database/migrations')) {
            mkdir(ZEN_PATH . '/app/Database/migrations', 0777, true);
        }

        if (self::writePhpFile($path, $template)) {
            CliOutput::success("\n✓ Migration {$name} created successfully!");
        } else {
            CliOutput::error("\n✗ Error: Could not create migration.");
        }
    }
}
