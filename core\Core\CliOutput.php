<?php

namespace Zen\Core;

/**
 * Class CliOutput
 * Provides static methods for formatted command-line interface (CLI) output.
 * Includes functionalities for displaying colored text, headers, sections, lists, tables,
 * prompts, confirmations, and progress bars.
 */
class CliOutput
{
    // Foreground Colors
    private const FG_BLACK = '0;30';
    private const FG_RED = '0;31';
    private const FG_GREEN = '0;32';
    private const FG_YELLOW = '0;33';
    private const FG_BLUE = '0;34';
    private const FG_MAGENTA = '0;35';
    private const FG_CYAN = '0;36';
    private const FG_WHITE = '0;37';
    private const FG_DEFAULT = '0;39';

    // Background Colors
    private const BG_BLACK = '40';
    private const BG_RED = '41';
    private const BG_GREEN = '42';
    private const BG_YELLOW = '43';
    private const BG_BLUE = '44';
    private const BG_MAGENTA = '45';
    private const BG_CYAN = '46';
    private const BG_WHITE = '47';
    private const BG_DEFAULT = '49';

    // Text Styles
    private const STYLE_BOLD = '1';
    private const STYLE_DIM = '2';
    private const STYLE_ITALIC = '3';
    private const STYLE_UNDERLINE = '4';
    private const STYLE_BLINK = '5';
    private const STYLE_REVERSE = '7';
    private const STYLE_HIDDEN = '8';
    private const STYLE_RESET = '0';

    private static $positiveResponses = [
        "active", "exists", "inline", "callable", "installed", "updated", "installed", "created", "moved", "copied"
    ];
    private static $warningResponses = [
        "unused"
    ];
    private static $negativeResponses = [
        "inactive", "missing", "uninstalled", "deleted", "removed"
    ];

    /**
     * Displays the Zen Framework CLI tool introduction ASCII art and header.
     * @return void
     */
    public static function showIntro(): void
    {
        $zenArt = [
            "███████╗███████╗███╗   ██╗",
            "╚══███╔╝██╔════╝████╗  ██║",
            "  ███╔╝ █████╗  ██╔██╗ ██║",
            " ███╔╝  ██╔══╝  ██║╚██╗██║",
            "███████╗███████╗██║ ╚████║",
            "╚══════╝╚══════╝╚═╝  ╚═══╝"
        ];

        $colors = [self::FG_CYAN, self::FG_CYAN, self::FG_CYAN];
        $colorIndex = 0;

        echo "\n\n";
        foreach ($zenArt as $line) {
            self::writeln($line, $colors[$colorIndex], self::STYLE_BOLD);
            $colorIndex = ($colorIndex + 1) % count($colors);
        }

        self::writeln("\n╭" . str_repeat("─", 28) . "╮", self::FG_WHITE);
        self::writeln("│  " . self::colorize("Zen Framework CLI Tool", self::FG_CYAN, self::STYLE_BOLD) . "    │", self::FG_WHITE);
        self::writeln("╰" . str_repeat("─", 28) . "╯", self::FG_WHITE);
        echo "\n";
    }

    /**
     * Prints a success message in green color.
     * @param string $message The message to print.
     * @return void
     */
    public static function success(string $message): void
    {
        self::writeln($message, self::FG_GREEN);
    }

    /**
     * Prints an error message in red color.
     * @param string $message The message to print.
     * @return void
     */
    public static function error(string $message): void
    {
        self::writeln($message, self::FG_RED);
    }

    /**
     * Prints a warning message in yellow color.
     * @param string $message The message to print.
     * @return void
     */
    public static function warning(string $message): void
    {
        self::writeln($message, self::FG_YELLOW);
    }

    /**
     * Prints an informational message in cyan color.
     * @param string $message The message to print.
     * @return void
     */
    public static function info(string $message): void
    {
        self::writeln($message, self::FG_CYAN);
    }

    /**
     * Prints a header with a separator line in cyan color.
     * @param string $message The header message.
     * @return void
     */
    public static function header(string $message): void
    {
        echo "\n";
        self::writeln("  {$message}  ", self::FG_CYAN, self::STYLE_BOLD);
        self::writeln(str_repeat('-', strlen($message) + 4), self::FG_CYAN, self::STYLE_BOLD);
        echo "\n";
    }

    /**
     * Prints a section header with a bullet point and separator line in magenta color.
     * @param string $message The section message.
     * @return void
     */
    public static function section(string $message): void
    {
        echo "\n";
        self::writeln("▶ {$message}", self::FG_MAGENTA, self::STYLE_BOLD);
        self::writeln(str_repeat('-', strlen($message) + 2), self::FG_MAGENTA);
    }

    /**
     * Gets the current terminal width.
     * @return int The terminal width in columns.
     */
    private static function getTerminalWidth(): int
    {
        if (stripos(PHP_OS_FAMILY, 'Windows') !== false) {
            $output = [];
            exec('mode con', $output);
            foreach ($output as $line) {
                if (preg_match('/Columns:\s+(\d+)/i', $line, $matches)) {
                    return (int)$matches[1];
                }
            }
            return 80;
        }

        // Unix fallback
        return self::getTerminalWidthUnix();
    }

    /**
     * Gets the terminal width for Unix-like systems.
     * @return int The terminal width in columns.
     */
    private static function getTerminalWidthUnix(): int
    {
        $width = (int)trim(shell_exec('tput cols'));
        return $width > 0 ? $width : 80;
    }

    /**
     * Prints a list of items with a specified bullet point.
     * @param array $items An array of strings to list.
     * @param string $bullet The bullet character to use (default: '•').
     * @return void
     */
    public static function list(array $items, string $bullet = '•'): void
    {
        $terminalWidth = self::getTerminalWidth();
        $maxItemWidth = $terminalWidth - 4; // Account for bullet and padding

        foreach ($items as $item) {
            $wrappedItem = wordwrap($item, $maxItemWidth, "\n    ");
            self::writeln("{$bullet} {$wrappedItem}", self::FG_WHITE);
        }
    }

    /**
     * Prints a formatted table with headers and rows.
     * Automatically adjusts column widths and truncates content if necessary.
     * @param array $headers An array of table headers.
     * @param array $rows A 2D array of table rows.
     * @return void
     */
    public static function table(array $headers, array $rows): void
    {
        $terminalWidth = self::getTerminalWidth();
        $columnCount = count($headers);
        $padding = 3; // Space for column separators and padding
        $availableWidth = $terminalWidth - ($padding * ($columnCount + 1));

        // Calculate optimal column widths based on content
        $widths = array_fill(0, $columnCount, 0);

        // Check header lengths
        foreach ($headers as $i => $header) {
            $widths[$i] = max($widths[$i], strlen($header));
        }

        // Check row content lengths
        foreach ($rows as $row) {
            foreach ($row as $i => $cell) {
                if (isset($widths[$i])) {
                    $widths[$i] = max($widths[$i], strlen($cell));
                }
            }
        }

        // If total width exceeds available space, adjust proportionally
        $totalContentWidth = array_sum($widths);
        if ($totalContentWidth > $availableWidth) {
            $ratio = $availableWidth / $totalContentWidth;
            foreach ($widths as &$width) {
                $width = max(3, (int)floor($width * $ratio)); // At least 3 chars (for "...")
            }
        }

        // Print headers
        echo "\n";
        self::printTableRow($headers, $widths, self::FG_CYAN, self::STYLE_BOLD);

        // Print separator line
        $separatorLength = array_sum($widths) + ($columnCount * 3) + 1;
        self::writeln(str_repeat('-', $separatorLength), self::FG_CYAN);

        // Print rows with truncation
        foreach ($rows as $row) {
            self::printTableRow($row, $widths, self::FG_WHITE);
        }
        echo "\n";
    }

    /**
     * Prints a single table row, applying colors and truncation as needed.
     * @param array $cells An array of cell contents for the row.
     * @param array $widths An array of column widths.
     * @param string $color The default foreground color for the row.
     * @param string|null $style Optional. The text style for the row.
     * @return void
     */
    private static function printTableRow(array $cells, array $widths, string $color, ?string $style = null): void
    {
        $row = '| ';
        foreach ($cells as $i => $cell) {
            if (!isset($widths[$i])) {
                continue; // Skip if no width defined for this column
            }

            $cellColor = null;
            // Determine cell color based on content
            if (in_array(strtolower(trim($cell)), self::$positiveResponses)) {
                $cellColor = self::FG_GREEN;
            } elseif (in_array(strtolower(trim($cell)), self::$warningResponses)) {
                $cellColor = self::FG_YELLOW;
            } elseif (in_array(strtolower(trim($cell)), self::$negativeResponses)) {
                $cellColor = self::FG_RED;
            }

            // Truncate if needed
            if (strlen($cell) > $widths[$i]) {
                $cell = substr($cell, 0, $widths[$i] - 3) . '...';
            }

            // Pad the cell to the correct width first
            $paddedCell = str_pad($cell, $widths[$i]);

            // Then apply color if needed
            $formattedCell = $cellColor
                ? self::colorize($paddedCell, $cellColor, $style)
                : $paddedCell;

            $row .= $formattedCell . ' | ';
        }

        self::writeln(trim($row), $color, $style);
    }

    /**
     * Prompts the user for input.
     * @param string $message The prompt message.
     * @param bool $required Whether the input is required (default: true).
     * @return string The user's input.
     */
    public static function prompt(string $message, bool $required = true): string
    {
        self::write("{$message}: ", self::FG_YELLOW);
        $input = trim(fgets(STDIN));

        if ($required && empty($input)) {
            self::error("A value is required!");
            return self::prompt($message, $required);
        }

        return $input;
    }

    /**
     * Prompts the user for a yes/no confirmation.
     * @param string $message The confirmation message.
     * @return bool True if the user confirms (y/yes), false otherwise.
     */
    public static function confirm(string $message): bool
    {
        self::write("{$message} (y/n): ", self::FG_YELLOW);
        $input = strtolower(trim(fgets(STDIN)));
        return $input === 'y' || $input === 'yes';
    }

    /**
     * Displays a progress bar in the console.
     * @param int $current The current progress value.
     * @param int $total The total value for the progress bar.
     * @return void
     */
    public static function progressBar(int $current, int $total): void
    {
        $percent = ($current / $total) * 100;
        $bar = str_repeat('█', (int)($percent / 2));
        $bar .= str_repeat('░', 50 - strlen($bar));

        self::write("\r[{$bar}] " . number_format($percent, 1) . '%', self::FG_CYAN);
        if ($current === $total) {
            echo "\n";
        }
    }

    /**
     * Writes a message to the console with specified color and style.
     * @param string $message The message to write.
     * @param string $color The foreground color constant (e.g., `self::FG_GREEN`).
     * @param string|null $style Optional. The text style constant (e.g., `self::STYLE_BOLD`).
     * @return void
     */
    private static function write(string $message, string $color, ?string $style = null): void
    {
        echo self::colorize($message, $color, $style);
    }

    /**
     * Writes a message to the console with a newline, specified color and style.
     * @param string $message The message to write.
     * @param string $color The foreground color constant (e.g., `self::FG_GREEN`).
     * @param string|null $style Optional. The text style constant (e.g., `self::STYLE_BOLD`).
     * @return void
     */
    private static function writeln(string $message, string $color, ?string $style = null): void
    {
        self::write($message . PHP_EOL, $color, $style);
    }

    /**
     * Applies ANSI escape codes for color and style to a string.
     * @param string $message The message to colorize.
     * @param string $color The foreground color constant.
     * @param string|null $style Optional. The text style constant.
     * @return string The colorized string.
     */
    private static function colorize(string $message, string $color, ?string $style = null): string
    {
        $styleStr = $style ? "\033[{$style}m" : '';
        return "\033[{$color}m{$styleStr}{$message}\033[0m";
    }
}
