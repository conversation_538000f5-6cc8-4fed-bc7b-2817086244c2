<?php

namespace Zen\Core;

use Zen\Core\Http\Request;
use Zen\Core\Http\Response;

/**
 * Abstract base controller class for all application controllers.
 * Provides request/response handling and utility methods for rendering and redirection.
 */
abstract class Controller
{
    /**
     * @var Request HTTP request instance.
     */
    protected Request $request;
    /**
     * @var Response HTTP response instance.
     */
    protected Response $response;

    /**
     * Controller constructor. Initializes request and response objects.
     */
    public function __construct()
    {
        $this->request = new Request();
        $this->response = new Response();
    }

    /**
     * Redirect to another URL.
     * @param string $url URL to redirect to
     * @param int $statusCode HTTP status code (default: 302)
     * @return void
     */
    protected function redirect(string $url, int $statusCode = 302): void
    {
        $this->response->setRedirect($url, $statusCode)
            ->send();
    }

    /**
     * Render a view with data.
     * @param string $view View name
     * @param array $data Data to pass to view
     * @return string Rendered content
     */
    protected function render(string $view, array $data = []): string
    {
        return \Zen\Core\Theme::render($view, $data);
    }

    /**
     * Send an HTML response
     *
     * @param string $view View name
     * @param array $data Data to pass to view
     * @param int $status HTTP status code
     */
    protected function view(string $view, array $data = [], int $status = 200): void
    {
        $computedContent = $this->render($view, $data);
        $this->response->setHeader('Content-Type', 'text/html')
            ->setContent($computedContent)
            ->setStatus($status)
            ->send();
    }

    /**
     * Send a JSON response
     *
     * @param mixed $data Data to encode as JSON
     * @param int $status HTTP status code
     * @param array $headers Additional headers
     */
    protected function json($data, int $status = 200, array $headers = []): void
    {
        $content = is_string($data) ? $data : json_encode($data, JSON_PRETTY_PRINT);
        
        $this->response->setHeader('Content-Type', 'application/json')
            ->setHeaders($headers)
            ->setContent($content)
            ->setStatus($status)
            ->send();
    }

    /**
     * Send a plain text response
     *
     * @param string $content Text content
     * @param int $status HTTP status code
     * @param array $headers Additional headers
     */
    protected function text(string $content, int $status = 200, array $headers = []): void
    {
        $this->response->setHeader('Content-Type', 'text/plain')
            ->setHeaders($headers)
            ->setContent($content)
            ->setStatus($status)
            ->send();
    }

    /**
     * Stream content to client
     *
     * @param string $content Content to stream
     * @param string $contentType Content type
     * @param array $headers Additional headers
     */
    protected function stream(string $content, string $contentType = 'application/octet-stream', array $headers = []): void
    {
        $this->response->setHeader('Content-Type', $contentType)
            ->setHeaders($headers)
            ->setHeader('Content-Length', strlen($content))
            ->setContent($content)
            ->send();
    }

    /**
     * Stream a file to client with support for range requests (ideal for video/audio)
     *
     * @param string $filePath Path to file
     * @param string|null $downloadName Custom name for download
     * @return void
     */
    public function streamFile(string $filePath, ?string $downloadName = null): void
    {
        if (!file_exists($filePath)) {
            $this->response->setStatus(404)->send();
            return;
        }

        $size = filesize($filePath);
        $contentType = mime_content_type($filePath);
        $start = 0;
        $end = $size - 1;
        $status = 200;

        // Handle range request
        if ($this->request->getRange() !== null) {
            $status = 206;
            list(, $range) = explode('=', $this->request->getRange(), 2);
            
            if (strpos($range, ',') !== false) {
                $this->response->setStatus(416)
                    ->setHeader('Content-Range', 'bytes */' . $size)
                    ->send();
                return;
            }
            
            if (preg_match('/(\d*)-(\d*)/', $range, $matches)) {
                $start = empty($matches[1]) ? 0 : intval($matches[1]);
                $end = empty($matches[2]) ? $size - 1 : intval($matches[2]);
                
                if ($start > $end || $start >= $size || $end >= $size) {
                    $this->response->setStatus(416)
                        ->setHeader('Content-Range', 'bytes */' . $size)
                        ->send();
                    return;
                }
            }
        }

        $length = $end - $start + 1;

        // Create streamed response
        $this->response->stream(function() use ($filePath, $start, $end) {
            $handle = fopen($filePath, 'rb');
            fseek($handle, $start);
            $remaining = $end - $start + 1;
            $buffer = 8192;

            while (!feof($handle) && $remaining > 0) {
                $chunkSize = min($buffer, $remaining);
                $data = fread($handle, $chunkSize);
                echo $data;
                flush();
                $remaining -= strlen($data);

                if (connection_aborted()) {
                    break;
                }
            }

            fclose($handle);
        }, $status, [
            'Content-Type' => $contentType,
            'Content-Length' => $length,
            'Accept-Ranges' => 'bytes',
            'Content-Range' => $status === 206 ? "bytes $start-$end/$size" : null,
            'Content-Disposition' => $downloadName ? 'attachment; filename="' . $downloadName . '"' : 'inline',
            'Cache-Control' => 'no-cache, must-revalidate, max-age=0'
        ]);

        $this->response->send();
    }

    /**
     * Download a file
     *
     * @param string $filePath Path to file
     * @param string|null $downloadName Custom name for download
     */
    protected function downloadFile(string $filePath, ?string $downloadName = null): void
    {
        $this->streamFile($filePath, $downloadName);
    }

    /**
     * Send a "No Content" response
     *
     * @param array $headers Additional headers
     */
    protected function noContent(array $headers = []): void
    {
        $this->response->setHeaders($headers)
            ->setStatus(204)
            ->send();
    }

    /**
     * Send a "Not Found" response
     *
     * @param string $message Optional message
     * @param array $headers Additional headers
     */
    protected function notFound(string $message = 'Not Found', array $headers = []): void
    {
        $this->response->setHeaders($headers)
            ->setContent($message)
            ->setStatus(404)
            ->send();
    }

    /**
     * Send an "Unauthorized" response
     *
     * @param string $message Optional message
     * @param array $headers Additional headers
     */
    protected function unauthorized(string $message = 'Unauthorized', array $headers = []): void
    {
        $this->response->setHeaders($headers)
            ->setContent($message)
            ->setStatus(401)
            ->send();
    }

    /**
     * Send a "Forbidden" response
     *
     * @param string $message Optional message
     * @param array $headers Additional headers
     */
    protected function forbidden(string $message = 'Forbidden', array $headers = []): void
    {
        $this->response->setHeaders($headers)
            ->setContent($message)
            ->setStatus(403)
            ->send();
    }

    /**
     * Send a "Bad Request" response
     *
     * @param string $message Optional message
     * @param array $headers Additional headers
     */
    protected function badRequest(string $message = 'Bad Request', array $headers = []): void
    {
        $this->response->setHeaders($headers)
            ->setContent($message)
            ->setStatus(400)
            ->send();
    }

    /**
     * Send a "Method Not Allowed" response
     *
     * @param array $allowedMethods Allowed HTTP methods
     * @param string $message Optional message
     * @param array $headers Additional headers
     */
    protected function methodNotAllowed(array $allowedMethods, string $message = 'Method Not Allowed', array $headers = []): void
    {
        $headers['Allow'] = implode(', ', $allowedMethods);
        
        $this->response->setHeaders($headers)
            ->setContent($message)
            ->setStatus(405)
            ->send();
    }

    /**
     * Stream multiple content pieces as they become available
     * 
     * @param array $contentProviders Array of callables that return content
     * @param array $headers Additional headers to set (optional)
     * @param int $status HTTP status code (default: 200)
     */
    protected function streamContent(array $contentProviders, array $headers = [], int $status = 200): void 
    {
        $defaultHeaders = [
            'Content-Type' => 'text/html; charset=utf-8',
            'X-Accel-Buffering' => 'no',
            'Cache-Control' => 'no-cache'
        ];

        $headers = array_merge($defaultHeaders, $headers);

        $this->response->stream(function() use ($contentProviders) {
            if (ob_get_level()) ob_end_clean();
            
            foreach ($contentProviders as $provider) {
                if (!is_callable($provider)) {
                    continue;
                }

                try {
                    $content = $provider();
                    echo $content;

                    if (ob_get_level() > 0) ob_flush();

                    flush();
                } catch (\Throwable $e) {
                    echo "Error: " . $e->getMessage();

                    if (ob_get_level() > 0) ob_flush();

                    flush();
                }
            }
        }, $status, $headers)->send();
    }

    /**
     * Stream JSON data as it becomes available
     * 
     * @param array $contentProviders Array of callables that return array/object to be JSON encoded
     * @param array $headers Additional headers to set (optional)
     * @param int $status HTTP status code (default: 200)
     * @param bool $wrapInArray Whether to wrap all items in a JSON array (default: true)
     */
    protected function streamJson(array $contentProviders, array $headers = [], int $status = 200, bool $wrapInArray = true): void 
    {
        $defaultHeaders = [
            'Content-Type' => 'application/json; charset=utf-8',
            'X-Accel-Buffering' => 'no',
            'Cache-Control' => 'no-cache'
        ];

        $headers = array_merge($defaultHeaders, $headers);

        $this->response->stream(function() use ($contentProviders, $wrapInArray) {
            if (ob_get_level()) ob_end_clean();
            
            if ($wrapInArray) echo '[';
            $first = true;

            foreach ($contentProviders as $provider) {
                if (!is_callable($provider)) continue;

                try {
                    $data = $provider();
                    
                    // Add comma if not first item and we're wrapping in array
                    if (!$first && $wrapInArray) {
                        echo ',';
                    }
                    $first = false;

                    // If data is already a JSON string, output it directly
                    if (is_string($data) && $this->isJson($data)) {
                        echo $wrapInArray ? trim($data) : $data;
                    } else {
                        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                    }

                    if (ob_get_level() > 0) ob_flush();
                    flush();
                } catch (\Throwable $e) {
                    // Handle error without breaking JSON syntax
                    if (!$first && $wrapInArray) echo ',';
                    echo json_encode(['error' => $e->getMessage()]);
                    if (ob_get_level() > 0) ob_flush();
                    flush();
                }
            }

            if ($wrapInArray) echo ']';
        }, $status, $headers)->send();
    }

    /**
     * Check if a string is valid JSON
     * 
     * @param string $string
     * @return bool
     */
    private function isJson(string $string): bool 
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }
}
