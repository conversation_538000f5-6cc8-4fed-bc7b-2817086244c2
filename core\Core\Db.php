<?php

namespace Zen\Core;

use Zen\Core\Error;

/**
 * Class Db
 * Handles database connection and query logic for multiple drivers.
 */
class Db
{
    protected static $_log = true;
    protected static $drivers = ['mysqli', 'pdo', 'mysql', 'pdo_mysql', 'pdo_sqlite', 'sqlite3', 'pdo_pgsql', 'pgsql', 'pdo_oci', 'pdo_sqlsrv', 'sqlsrv', 'oci8', 'ibm_db2'];
    protected static $pdo = null;
    protected static $mysqli = null;
    protected static $_init = false ;
    protected static $dbDriver = 'pdo'; // 'pdo' or 'mysqli / mysql'
    protected static $host;
    protected static $port;
    protected static $username;
    protected static $password;
    protected static $database;
    protected static $charset;
    protected static $ssl; // boolean
    protected static $cacert;
    protected static $bindings = [];
    protected static $dbal = null;
    protected static $defaultCert = ZEN_PATH . '/storage/secure/cacert.pem';

    /**
     * Initialize the database connection using configuration.
     * @param bool $ping
     * @return void
     */
    public static function init($ping = false)
    {
        if (!self::$_init) {
            $config = \Zen\Engine::loadConfig('database');

            self::$dbDriver = in_array(strtolower($config['driver']), self::$drivers) ? strtolower($config['driver']) : 'pdo';
            self::$host = $config['host'];
            self::$port = isset($config['port']) ? $config['port'] : '3306';
            self::$username = $config['username'];
            self::$password = $config['password'];
            self::$database = $config['database'];
            self::$charset = isset($config['charset']) ? $config['charset'] : 'utf8mb4';
            self::$ssl = $config['ssl'] ? true : false;
            self::$cacert = isset($config['ssl_cacert']) ? $config['ssl_cacert'] : self::$defaultCert;
            self::$_init = true;
        }

        if (!self::check($ping)) {
            self::connect();
        }
    }

    protected static function connect()
    {
        try {
            if (self::is_pdo()) {
                $options = [
                    \PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES ".self::$charset
                ];

                if (self::$ssl) {
                    $options[\PDO::MYSQL_ATTR_SSL_CA] = self::$cacert;
                    $options[\PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT] = false;
                }

                $dsn = "mysql:dbname=" . self::$database . ";host=" . self::$host . ";port=" . self::$port;

                self::$pdo = new \PDO($dsn, self::$username, self::$password, $options);
                self::$pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
                self::$pdo->setAttribute(\PDO::ATTR_EMULATE_PREPARES, false);
            } elseif (self::is_mysqli()) {
                mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

                self::$mysqli = mysqli_init();

                if (self::$ssl) {
                    self::$mysqli->options(MYSQLI_OPT_SSL_VERIFY_SERVER_CERT, false);
                    self::$mysqli->options(MYSQLI_CLIENT_SSL_DONT_VERIFY_SERVER_CERT, true);
                    self::$mysqli->ssl_set(null, null, self::$cacert, null, null);
                }

                self::$mysqli->real_connect(self::$host, self::$username, self::$password, self::$database, self::$port);

                if (!empty(self::$charset)) {
                    mysqli_set_charset(self::$mysqli, self::$charset);
                }

                if (self::$mysqli->connect_error) {
                    return self::logError("Connection failed: " . self::$mysqli->connect_error, '', [], true);
                }
            } else {
                return self::logError("Invalid database type.", '', [], true);
            }
        } catch (\Exception $e) {
            return self::logError("Connection failed: " . $e->getMessage(), '', [], true);
        }
    }

    public static function is_pdo()
    {
        return strtolower(self::$dbDriver) === 'pdo';
    }

    public static function is_mysqli()
    {
        return strtolower(self::$dbDriver) === 'mysqli' || strtolower(self::$dbDriver) === 'mysql';
    }

    public static function disconnect()
    {
        self::init();
        if (self::is_pdo()) {
            self::$pdo = null;
        } elseif (self::is_mysqli()) {
            self::$mysqli->close();
        }
    }

    public static function transaction($queries)
    {
        self::init();
        try {
            if (self::is_pdo()) {
                self::$pdo->beginTransaction();
                foreach ($queries as $query) {
                    self::query($query['query'], $query['binds'], true);
                }
                self::$pdo->commit();
            } elseif (self::is_mysqli()) {
                /* Start transaction */
                self::$mysqli->begin_transaction();
                foreach ($queries as $query) {
                    self::query($query['query'], $query['binds'], true);
                }
                self::$mysqli->commit();
            } else {
                return self::logError("Invalid database type.");
            }

            return true;
        } catch (\Exception $e) {
            set_error_handler(function ($severity, $message, $file, $line) {
                throw new \ErrorException($message, 0, $severity, $file, $line);
            });

            try {
                if (self::is_pdo()) {
                    self::$pdo->rollBack();
                } elseif (self::is_mysqli()) {
                    self::$mysqli->rollback();
                    // self::$mysqli->autocommit(true);
                }
                return self::logError("Transaction failed: " . $e->getMessage());
            } catch (\Exception $ee) {
                restore_error_handler();
                return self::logError("Transaction & rollback failed: " . $ee->getMessage());
            }
        }
    }

    public static function bind($param, $value)
    {
        // Additional function to bind parameters
        self::$bindings[$param] = $value;
    }

    public static function correct_binds($query, $binds)
    {
        preg_match_all('/:([a-zA-Z0-9_]+)/', $query, $matches);
        $placeholders = $matches[1];

        $corrected_binds = array_intersect_key($binds, array_flip($placeholders));

        return $corrected_binds;
    }

    protected static function detectType($value)
    {
        if (self::is_pdo()) {
            switch (true) {
                case is_bool($value):
                    return \PDO::PARAM_BOOL;
                case is_int($value):
                    return \PDO::PARAM_INT;
                case is_null($value):
                    return \PDO::PARAM_NULL;
                default:
                    return \PDO::PARAM_STR;
            }
        } elseif (self::is_mysqli()) {
            switch (true) {
                case is_int($value):
                    return 'i'; // Integer
                case is_float($value):
                    return 'd'; // Double
                default:
                    return 's'; // String
            }
        }
    }

    public static function query($query, $binds = [], $transaction = false)
    {
        $query = trim(rtrim(ltrim($query, " "), " "));

        self::init();
        try {

            // Merge user-provided binds with pre-defined binds
            $binds = array_merge(self::$bindings, $binds);

            // Remove unused binds
            $binds = self::correct_binds($query, $binds);

            if (self::is_pdo()) {
                $stmt = self::$pdo->prepare($query);
                foreach ($binds as $param => $value) {
                    $stmt->bindValue(":" . $param, $value/*, self::detectType($value)*/);
                }
                $queryResult = $stmt->execute();

                // Reset bindings after execution
                self::$bindings = [];

                // extract action type
                $rawStatement = explode(" ", $query);
                $statement = strtolower(trim($rawStatement[0], ' '));

                // SELECT or SHOW queries should return data in an array
                if (in_array($statement, ['select', 'show'])) {
                    return $stmt->fetchAll(\PDO::FETCH_ASSOC);
                } elseif (in_array($statement, ['insert', 'update', 'delete'])) {
                    return $stmt->rowCount();
                } else {
                    return $queryResult;
                }
            } elseif (self::is_mysqli()) {

                // Extract parameters from the query and prepare for binding
                preg_match_all("/:(\w+)/", $query, $paramMatches);
                $preparedParams = [];

                // Check if there are parameters to bind and if values are provided
                if (!empty($paramMatches[1]) && is_array($paramMatches[1]) && !empty($binds)) {

                    // Initialize types and parameters arrays
                    $preparedParams[0] = "";

                    // Process each parameter in the query
                    foreach ($paramMatches[1] as $paramName) {
                        // Replace parameter in the query with a placeholder
                        $pattern = "/:" . $paramName . "\\b/m";
                        $query = preg_replace($pattern, "?", $query);

                        // Determine the type and append to types string
                        $preparedParams[0] .= self::detectType($binds[$paramName]);

                        // Reference the parameter for binding
                        $preparedParams[] = &$binds[$paramName];
                    }
                }

                $stmt = self::$mysqli->prepare($query);

                if (!empty($preparedParams)) {
                    call_user_func_array(array($stmt, "bind_param"), $preparedParams);
                }
                /*
                foreach ($preparedParams as $param => $value) {
                    $stmt->bind_param('s', $value); // Assuming all parameters are strings
                }*/
                $queryResult = $stmt->execute();

                // Reset bindings after execution
                self::$bindings = [];

                // store results
                $result = $stmt->get_result();

                // SELECT or SHOW queries should return data in an array
                if (stripos($query, 'SELECT') === 0 || stripos($query, 'SHOW') === 0) {
                    return $result->fetch_all(MYSQLI_ASSOC);
                }

                $stmt->free_result();

                // For other queries (INSERT, UPDATE, DELETE), return boolean
                return $queryResult;
            } else {
                return self::logError("Invalid database type.");
            }
            // } catch (\Throwable $e) { // Throwable can be used for php 8
        } catch (\Exception $e) { // Throwable can be used for php 8

            // Reset bindings on exception
            self::$bindings = [];
            if ($transaction) {
                throw new \Exception("Query execution failed: " . $e->getMessage());
            } else {
                return self::logError("Query failed: " . $e->getMessage(), $query, $binds);
            }
        }
    }

    public static function import($sql)
    {
        self::init();
        try {
            if (self::is_pdo()) {
                self::$pdo->exec($sql);
            } elseif (self::is_mysqli()) {
                if (self::$mysqli->multi_query($sql)) {
                    do {
                        // Consume all results to avoid errors in subsequent queries
                        if ($result = self::$mysqli->store_result()) {
                            $result->free();
                        }
                    } while (self::$mysqli->more_results() && self::$mysqli->next_result());
                } else {
                    return self::logError("Import failed: " . self::$mysqli->error);
                }
                self::$mysqli->close();
            } else {
                return self::logError("Invalid database type.");
            }
        } catch (\Exception $e) {
            return self::logError("Import failed: " . $e->getMessage());
        }
    }


    public static function check($ping = false)
    {
        // Check if the database connection is active
        if (self::is_pdo()) {
            return self::$pdo != null;
        } elseif (self::is_mysqli()) {
            if ($ping) {
                return self::$mysqli != null ? self::$mysqli->ping() : false;
            } else {
                return self::$mysqli != null;
            }
        } else {
            return self::logError("Invalid database type.");
        }
    }

    public static function last_id()
    {
        self::init();

        // Get the last inserted ID
        if (self::is_pdo()) {
            return self::$pdo->lastInsertId();
        } elseif (self::is_mysqli()) {
            return self::$mysqli->insert_id;
        } else {
            return self::logError("Invalid database type.");
        }
    }

    public static function all_tables()
    {
        // Get all table names in the database
        $query = "SHOW TABLES";
        $result = self::query($query);

        $tables = [];
        if (!empty($tables)) {
            foreach ($result as $row) {
                $tables[] = reset($row); // Assuming the table name is the first element in each row
            }
        }

        return $tables;
    }

    protected static function errors($message, $sql = "", $binds = [])
    {
        if (!empty($sql)) {
            $message .= "\nQuery: "  . $sql;
            $message .= "\nParams: " . var_export($binds, true);
        }

        return $message;
    }

    public static function logError($error, $query = '', $binds = [], $force = false)
    {
        if (self::$_log) {
            if (!empty($query)) {
                Error::createLog('database', self::errors($error, $query, $binds));
            } else {
                Error::createLog('database', $error);
            }
        }

        $env = \Zen\Engine::loadConfig('environment');
        if ($env == 'testing' || $env == 'development' || $force) {
            Error::print($error, true, 500, $force);
        }

        return false;
    }

    public static function exposeAdapter()
    {
        self::init();
        if (self::is_pdo()) {
            return self::$pdo;
        } elseif (self::is_mysqli()) {
            return self::$mysqli;
        }
    }

    public static function dbal()
    {
        self::init();
        if(is_null(self::$dbal)) {
            $connectionParams = [
                'dbname' => self::$database,
                'user' => self::$username,
                'password' => self::$password,
                'host' => self::$host,
                'port' => self::$port,
                'charset' => self::$charset,
                'driver' => self::is_pdo() ? 'pdo_mysql' : self::$dbDriver,
            ];

            if(self::$ssl) {
                $connectionParams['ssl_capath'] = self::$cacert;
            }

            self::$dbal = \Doctrine\DBAL\DriverManager::getConnection($connectionParams);
        }

        return self::$dbal;
    }
}
