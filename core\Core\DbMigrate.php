<?php

namespace Zen\Core;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\Comparator;

use Zen\Core\Db;
use Zen\Core\CliOutput;

class DbMigrate
{
    private $dbal;
    private $migrations_path;

    public function __construct()
    {
        $this->dbal = Db::dbal();
        $this->migrations_path = ZEN_PATH . '/app/Database/migrations/';
    }

    public function run()
    {
        $this->createMigrationsTable();

        $ran_migrations = $this->getRanMigrations();
        $migration_files = $this->getMigrationFiles();

        foreach ($migration_files as $migration) {
            if (!in_array($migration, $ran_migrations)) {
                $this->runMigration($migration);
            }
        }

        CliOutput::success('Migrations ran successfully.');
    }

    public function rollback($steps = 1)
    {
        $ran_migrations = $this->getRanMigrations();

        if (empty($ran_migrations)) {
            CliOutput::info('No migrations to rollback.');
            return;
        }

        $migrations_to_rollback = $steps == 'all' ? $ran_migrations :  array_slice($ran_migrations, -$steps);

        foreach ($migrations_to_rollback as $migration) {
            $this->_processMigration($migration, 'down');
        }

        CliOutput::success('Rollback completed successfully.');
    }

    private function createMigrationsTable()
    {
        $schemaManager = $this->dbal->createSchemaManager();

        if (!$schemaManager->tablesExist(['migrations'])) {
            $schema = new Schema();
            $table = $schema->createTable('migrations');
            $table->addColumn('id', 'integer', ['unsigned' => true, 'autoincrement' => true]);
            $table->addColumn('migration', 'string', ['length' => 255]);
            $table->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP']);
            $table->setPrimaryKey(['id']);

            $queries = $schema->toSql($this->dbal->getDatabasePlatform());
            foreach ($queries as $query) {
                $this->dbal->executeQuery($query);
            }
            CliOutput::info('Migrations table created.');
        }
    }

    private function getRanMigrations()
    {
        $schemaManager = $this->dbal->createSchemaManager();
        if (!$schemaManager->tablesExist(['migrations'])) {
            return [];
        }

        $query = $this->dbal->createQueryBuilder()
            ->select('migration')
            ->from('migrations')
            ->orderBy('id', 'DESC');

        return $query->executeQuery()->fetchFirstColumn();
    }

    private function getMigrationFiles()
    {
        if (!is_dir($this->migrations_path)) {
            return [];
        }
        $files = scandir($this->migrations_path);
        $migrations = [];
        foreach ($files as $file) {
            if (preg_match('/\.php$/', $file)) {
                $migrations[] = $file;
            }
        }
        sort($migrations);
        return $migrations;
    }

    private function runMigration($migration)
    {
        $this->_processMigration($migration, 'up');
    }

    private function _processMigration($migration, $direction = 'up')
    {
        require_once $this->migrations_path . $migration;

        $className = preg_replace('/^[0-9]{4}_[0-9]{2}_[0-9]{2}_[0-9]{6}_/', '', pathinfo($migration, PATHINFO_FILENAME));
        $class = 'App\\Database\\Migrations\\' . $className;

        if (!class_exists($class)) {
            CliOutput::error("Class {$class} not found in migration file {$migration}");
            return;
        }

        $migration_instance = new $class();

        $schemaManager = $this->dbal->createSchemaManager();
        $fromSchema = $schemaManager->introspectSchema();
        $toSchema = clone $fromSchema;

        $migration_instance->{$direction}($toSchema);

        $comparator = $schemaManager->createComparator();
        $schemaDiff = $comparator->compareSchemas($fromSchema, $toSchema);

        $platform = $this->dbal->getDatabasePlatform();
        $sql = [];

        foreach ($schemaDiff->getCreatedSchemas() as $schema) {
            $sql = array_merge($sql, $platform->getCreateSchemaSQL($schema));
        }

        foreach ($schemaDiff->getDroppedSchemas() as $schema) {
            $sql = array_merge($sql, $platform->getDropSchemaSQL($schema));
        }

        foreach ($schemaDiff->getCreatedTables() as $table) {
            $sql = array_merge($sql, $platform->getCreateTableSQL($table));
        }

        foreach ($schemaDiff->getAlteredTables() as $tableDiff) {
            $sql = array_merge($sql, $platform->getAlterTableSQL($tableDiff));
        }

        foreach ($schemaDiff->getDroppedTables() as $table) {
            $sql[] = $platform->getDropTableSQL($table->getName());
        }

        foreach ($schemaDiff->getCreatedSequences() as $sequence) {
            $sql = array_merge($sql, $platform->getCreateSequenceSQL($sequence));
        }

        foreach ($schemaDiff->getAlteredSequences() as $sequence) {
            $sql = array_merge($sql, $platform->getAlterSequenceSQL($sequence));
        }

        foreach ($schemaDiff->getDroppedSequences() as $sequence) {
            $sql[] = $platform->getDropSequenceSQL((string)$sequence);
        }

        if (empty($sql)) {
            $action = ($direction === 'up') ? 'migrate' : 'rollback';
            CliOutput::info("Nothing to {$action} for {$migration}");
        } else {
            foreach ($sql as $query) {
                $this->dbal->executeQuery($query);
            }
            $action = ($direction === 'up') ? 'Migrated' : 'Rolled back';
            CliOutput::info("{$action}: " . $migration);
        }

        if ($direction === 'up') {
            $this->dbal->insert('migrations', ['migration' => $migration]);
        } else {
            $this->dbal->delete('migrations', ['migration' => $migration]);
        }
    }
}
