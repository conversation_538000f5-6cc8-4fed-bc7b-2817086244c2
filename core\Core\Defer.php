<?php

namespace Zen\Core;

/**
 * Class Defer
 * Allows deferring execution of tasks until later in the request lifecycle.
 */
class Defer
{
    /**
     * @var callable[] List of deferred tasks.
     */
    private static $tasks = [];

    /**
     * Add a task to be executed later.
     * @param callable $task
     * @return void
     */
    public static function set(callable $task)
    {
        self::$tasks[] = $task;
    }

    /**
     * Execute all deferred tasks.
     * @return void
     */
    public static function execute()
    {
        foreach (self::$tasks as $task) {
            try {
                call_user_func($task);
            } catch (\Exception $e) {
                // Handle exceptions if needed
            }
        }
    }
}
