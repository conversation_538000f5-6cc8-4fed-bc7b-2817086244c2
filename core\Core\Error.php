<?php

namespace Zen\Core;

use Zen\Core\Storage;

/**
 * Class Error
 * Handles error display and logging for the application.
 */
class Error
{
    /**
     * @var string HTML template for error display.
     */
    protected static $content = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>::title::</title><style>*{margin:0;padding:0}body{background:#000}.a{width:80%;height:auto;margin:50px auto}.b{margin:auto;width:80%;min-height:200px;height:100%;background:#111;border:.5px solid #262626;border-radius:7px;padding-bottom:20px}.c{width:100%;height:25px;border-radius:5px 5px 0 0}.d{grid-column-gap:1px;margin:10px 0 0 5px}.e{float:left;margin-left:7px;width:12px;height:12px;border-radius:100%}.e:hover{background:#000}.f{width:100%;height:100%;font-family:\'Source Code Pro\',monospace;font-size:.8rem;line-height:1}p{color:#fff;padding:5px 15px 0 15px;line-height: 18px;}.g{background:#fd5757}.h{background:#fbbd34}.i{background:#e4e4e4}@media screen and (max-width:767px){.a{width:calc(100% - 20px);padding:10px}.b{width:100%}.a{margin:10px auto}}a{color:#fbbd34;}</style></head><body><div class="a"><div class="b"><div class="c"><div class="d"><div class="e g"></div><div class="e h"></div><div class="e i"></div></div></div><div class="f"><p>> ::error::</p><p class="j"></p></div></div></div><script>const k=document.querySelector(\'.j\');const l=()=>setTimeout(()=>k.innerHTML=".",100)|setTimeout(()=>k.innerHTML="..",500)|setTimeout(()=>k.innerHTML="...",900);setInterval(l,1e3);</script></body></html>';
    /**
     * @var string Log rate: 'daily' or 'hourly'.
     */
    protected static $logRate = 'daily';

    /**
     * Print an error message to the user or log it, depending on environment.
     * @param string $error
     * @param bool $sendErrorHeader
     * @param int $errorCode
     * @param bool $force
     * @return void
     */
    public static function print($error = '', $sendErrorHeader = true, $errorCode = 500, $force = false)
    {
        if(defined('ZEN_CLI')) {
            \Zen\Core\CliOutput::error($error);
            return;
        }

        if (ob_get_contents()) {
            ob_clean();
        }

        $env = \Zen\Engine::loadConfig('environment');
        if($env == 'testing' || $env == 'development' || $force) {
            $printedCode = '';
            if ($sendErrorHeader) {
                if (!headers_sent()) {
                    http_response_code($errorCode);
                }
                $printedCode = ' ' . $errorCode;
            }

            echo str_replace(
                '::error::',
                'Error' . $printedCode . ': ' . $error,
                str_replace('::title::', 'Error' . $printedCode, self::$content)
            );
            exit;

        } else {
            Error::createLog('errors', $error);
        }
    }

    /**
     * Display an HTTP error page.
     * @param int $code HTTP error code (e.g., 404, 500, 403)
     * @param string|null $message Custom error message (optional)
     * @return void
     */
    public static function errorPage(int $code, ?string $message = null)
    {
        $errorMessages = [
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable'
        ];

        $errorMessage = $message ?? ($errorMessages[$code] ?? 'Unknown Error');
        http_response_code($code);
        echo str_replace(
            '::error::',
            "Error {$code}: {$errorMessage}",
            str_replace('::title::', "Error {$code}", self::$content)
        );
        exit;
    }

    /**
     * Display a 404 Not Found error page.
     * @return void
     */
    public static function notFound()
    {
        self::errorPage(404);
    }

    /**
     * Create or update an error log.
     * @param string $logType
     * @param string $message
     * @return void
     */
    public static function createLog($logType = "", $message = "")
    {
        $savePath = Storage::$logsPath . '/' . $logType . '/';
        $date = new \DateTime();

        if (strtolower(self::$logRate) == 'hourly') {
            $logDate = $date->format('Y-m-d') . "-" . $date->format('h');
        } else {
            $logDate = $date->format('Y-m-d');
        }

        $log = $savePath . $logDate . '.log';
        if (is_dir($savePath)) {
            if (!file_exists($log)) {
                $fo  = fopen($log, 'a+');
                $logcontent = "Time: " . $date->format('H:i:s') . "\r\n" . $message . "\r\n";
                fwrite($fo, $logcontent);
                fclose($fo);
            } else {
                self::editLog($log, $date, $message);
            }
        } else {
            self::prepareLogDirectory($logType);
            self::createLog($logType, $message);
        }
    }

    /**
     * Edit an existing log file.
     * @param string $log
     * @param \DateTime $date
     * @param string $message
     * @return void
     */
    private static function editLog($log, $date, $message)
    {
        $logcontent = "Date: " . $date->format('d M Y') . ", Time: " . $date->format('H:i:s') . "\r\n" . $message . "\r\n\r\n";
        $logcontent = $logcontent . self::readLog($log);
        @file_put_contents($log, $logcontent);
    }

    /**
     * Read the contents of a log file.
     * @param string $file
     * @return string|false
     */
    public static function readLog($file)
    {
        if (is_file($file)) {
            $handle = fopen($file, "r");
            $contents = filesize($file) > 0 ? fread($handle, filesize($file)) : '';
            fclose($handle);
            return $contents;
        } else {
            return false;
        }
    }

    /**
     * Prepare the log directory, creating it if it doesn't exist.
     * @param string $dirName
     * @param int $rights
     * @return bool
     */
    private static function prepareLogDirectory($dirName, $rights = 0777)
    {
        $dirs = explode('/', $dirName);
        $dir  = '';
        if (!empty($dirs)) {
            foreach ($dirs as $part) {
                $dir .= $part . '/';
                $expectedDir = ZEN_PATH . '/' . Storage::$logsPath . '/' . $dir;
                if (!file_exists($expectedDir) && !is_dir($dir) && strlen($dir) > 0) {
                    mkdir($expectedDir, $rights);
                    return true;
                }
            }
        } else {
            return false;
        }
    }
}
