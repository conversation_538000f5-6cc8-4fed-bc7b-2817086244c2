<?php

namespace Zen\Core;

/**
 * Class Event
 * Provides a simple event system for registering, triggering, and removing events.
 */
class Event
{
    /**
     * @var array Registered events.
     */
    protected static $events = [];
    /**
     * @var array Reserved event names that cannot be overridden.
     */
    protected static $reserved_events = [
        "__construct",
        "system",
        "core",
        "request_headers",
        "response_headers"
    ];

    /**
     * Initialize triggers on reserved events.
     * @return void
     */
    public static function init()
    {
        foreach(self::$reserved_events as $event) {
            self::trigger($event);
        }
    }

    /**
     * Register an event handler.
     * @param string $name
     * @param callable $fn
     * @param string $area
     * @return void
     */
    public static function set($name, $fn, $area = 'core')
    {
        if(!in_array($name, self::$reserved_events)) {
            self::$events[$name] = $fn;
        }
    }

    /**
     * Remove an event handler.
     * @param string $name
     * @return void
     */
    public static function remove($name)
    {
        unset(self::$events[$name]);
    }

    /**
     * Trigger an event
     *
     * @param  mixed $name
     * @param  array $args
     * @return void
     */
    public static function trigger($name, $args = [])
    {
        call_user_func(self::$events[$name], $args);
    }

    /**
     * Call all events
     *
     * @return void
     */
    public static function call()
    {
        if(!empty(self::$events)) {
            foreach(self::$events as $name => $fn) {
                self::trigger($name);
            }
        }
    }

}
