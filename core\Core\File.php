<?php

namespace Zen\Core;

/**
 * Class File
 * Provides static methods for secure file operations (read, write, delete, etc.).
 */
class File
{
    /**
     * Prevent access to core folders for security.
     * @param string $path
     * @throws \Exception
     */
    protected static function checkFolderAccess($path)
    {
        if (str_starts_with($path, ZEN_PATH . "/core") || str_starts_with($path, ZEN_PATH . "/core/") || str_starts_with($path, "./core") || str_starts_with($path, "core/") || $path == "core") {
            throw new \Exception("Access denied to folder: $path");
        }
    }

    /**
     * Open a file with the given permission.
     * @param string $path
     * @param string $permission
     * @return resource|false
     * @throws \Exception
     */
    protected static function open($path, $permission)
    {
        try {
            $fp = fopen($path, $permission);
            return $fp;
        } catch (\Exception $e) {
            throw new \Exception("Failed to open file on this path: $path -");
        }
        return false;
    }

    /**
     * Read the contents of a file.
     * @param string $filePath
     * @param int $expectedSize
     * @return string|false
     */
    public static function read($filePath, $expectedSize = -1)
    {
        self::checkFolderAccess($filePath);

        $file = self::open($filePath, 'r');
        if ($file) {
            $readSize = ($expectedSize == -1 && is_numeric($expectedSize)) ? filesize($filePath) : $expectedSize;
            $contents = $readSize > 0 ? fread($file, $readSize) : '';
            fclose($file);
            return $contents;
        }
        return false;
    }

    /**
     * Write data to a file.
     * @param string $filePath
     * @param string $data
     * @return bool
     */
    public static function write($filePath, $data)
    {
        self::checkFolderAccess($filePath);

        $file = self::open($filePath, 'w');
        if ($file) {
            $success = fwrite($file, $data);
            fclose($file);
            return $success !== false;
        }
        return false;
    }

    /**
     * Scan for files matching the given pattern.
     * @param string $search_pattern
     * @param int $flags
     * @return array
     */
    public static function scan($search_pattern, $flags = 0)
    {
        self::checkFolderAccess($search_pattern);

        return glob($search_pattern, $flags);
    }

    /**
     * Check if a file or directory exists.
     * @param string $filePath
     * @return bool
     */
    public static function exists($filePath)
    {
        return file_exists($filePath);
    }

    /**
     * Get the size of a file.
     * @param string $filePath
     * @return int
     */
    public static function size($filePath)
    {
        return filesize($filePath);
    }

    /**
     * Delete a file.
     * @param string $filePath
     * @return bool
     */
    public static function delete($filePath)
    {
        self::checkFolderAccess($filePath);

        return unlink($filePath);
    }

    /**
     * Copy a file from source to destination.
     * @param string $source
     * @param string $destination
     * @return bool
     */
    public static function copy($source, $destination)
    {
        self::checkFolderAccess($source);
        self::checkFolderAccess($destination);

        return copy($source, $destination);
    }

    /**
     * Move a file from source to destination.
     * @param string $source
     * @param string $destination
     * @return bool
     */
    public static function move($source, $destination)
    {
        self::checkFolderAccess($source);
        self::checkFolderAccess($destination);

        return rename($source, $destination);
    }

    /**
     * Create a directory.
     * @param string $path
     * @param int $permission
     * @param bool $recursive
     * @return bool
     */
    public static function makeDir($path, $permission = 0777, $recursive = true)
    {
        self::checkFolderAccess($path);

        if (!self::exists($path)) {
            return mkdir($path, $permission, $recursive);
        }
        return true;
    }

    /**
     * Recursively delete a directory.
     * @param string $dirPath
     * @return bool
     * @throws \Exception
     */
    public static function deleteDir($dirPath)
    {
        self::checkFolderAccess($dirPath);

        if (!is_dir($dirPath)) {
            return false;
        }

        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dirPath, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($files as $fileinfo) {
            $todo = ($fileinfo->isDir() ? 'rmdir' : 'unlink');
            $todo($fileinfo->getRealPath());
        }

        return rmdir($dirPath);
    }
}
