<?php

namespace Zen\Core;

use GraphQL\Type\Definition\ObjectType;
use GraphQL\Type\Definition\Type;
use GraphQL\Type\Schema;
use GraphQL\GraphQL as GraphQLProcessor;
use Zen\Core\Db;
use Zen\Core\Error;

/**
 * Class GraphQL
 * Provides a wrapper for GraphQL schema and query/mutation registration.
 */
class GraphQL
{
    /**
     * @var GraphQL|null Singleton instance.
     */
    private static $instance = null;
    /**
     * @var Schema|null GraphQL schema instance.
     */
    private $schema = null;
    /**
     * @var array Registered GraphQL types.
     */
    private $types = [];
    /**
     * @var array Registered query fields.
     */
    private $queryFields = [];
    /**
     * @var array Registered mutation fields.
     */
    private $mutationFields = [];
    /**
     * @var mixed Database abstraction layer instance.
     */
    private $db;

    /**
     * GraphQL constructor. Initializes DBAL.
     */
    private function __construct()
    {
        $this->db = Db::dbal();
    }

    /**
     * Gets the singleton instance of the GraphQL class.
     *
     * @return GraphQL The GraphQL instance.
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Adds a GraphQL type.
     *
     * @param string $name The name of the type.
     * @param ObjectType $type The ObjectType instance.
     * @return $this
     */
    public function addType($name, ObjectType $type)
    {
        $this->types[$name] = $type;
        return $this;
    }

    /**
     * Gets a registered GraphQL type by name.
     *
     * @param string $name The name of the type.
     * @return ObjectType|null The ObjectType instance or null if not found.
     */
    public function getType($name)
    {
        return $this->types[$name] ?? null;
    }

    /**
     * Adds a query field to the GraphQL schema.
     *
     * @param string $name The name of the query field.
     * @param array $field The field configuration.
     * @return $this
     */
    public function addQueryField($name, array $field)
    {
        $this->queryFields[$name] = $field;
        return $this;
    }

    /**
     * Adds a mutation field to the GraphQL schema.
     *
     * @param string $name The name of the mutation field.
     * @param array $field The field configuration.
     * @return $this
     */
    public function addMutationField($name, array $field)
    {
        $this->mutationFields[$name] = $field;
        return $this;
    }

    /**
     * Builds the GraphQL schema using registered query and mutation fields.
     *
     * @return $this
     */
    public function buildSchema()
    {
        $queryType = new ObjectType([
            'name' => 'Query',
            'fields' => $this->queryFields
        ]);

        $mutationType = !empty($this->mutationFields) ? new ObjectType([
            'name' => 'Mutation',
            'fields' => $this->mutationFields
        ]) : null;

        $this->schema = new Schema([
            'query' => $queryType,
            'mutation' => $mutationType
        ]);

        return $this;
    }

    /**
     * Executes a GraphQL query.
     *
     * @param string $query The GraphQL query string.
     * @param array|null $variables Optional. The variables for the query.
     * @param string|null $operationName Optional. The operation name.
     * @return array|null The query result as an array.
     */
    public function execute($query, $variables = null, $operationName = null)
    {
        try {
            if (!$this->schema) {
                $this->buildSchema();
            }

            $result = GraphQLProcessor::executeQuery(
                $this->schema,
                $query,
                null,
                null,
                $variables,
                $operationName
            );

            return $result->toArray();
        } catch (\Exception $e) {
            Error::print($e->getMessage());
        }
    }

    /**
     * Gets the database abstraction layer instance.
     *
     * @return mixed The DBAL instance.
     */
    public function getDb()
    {
        return $this->db;
    }

    /**
     * Helper method to create a basic CRUD type
     *
     * @param string $name The name of the entity type.
     * @param string $tableName The corresponding database table name.
     * @param array $fields The fields of the entity.
     * @return ObjectType The created ObjectType instance.
     */
    public function createEntityType($name, $tableName, array $fields)
    {
        $type = new ObjectType([
            'name' => $name,
            'fields' => function() use ($fields) {
                $typeFields = [];
                foreach ($fields as $fieldName => $fieldType) {
                    $typeFields[$fieldName] = ['type' => $fieldType];
                }
                return $typeFields;
            }
        ]);

        $this->addType($name, $type);

        // Add Query fields for this type
        $this->addQueryField(lcfirst($name), [
            'type' => $type,
            'args' => [
                'id' => Type::nonNull(Type::id())
            ],
            'resolve' => function($root, $args) use ($tableName) {
                $qb = $this->db->createQueryBuilder();
                return $qb->select('*')
                    ->from($tableName)
                    ->where('id = :id')
                    ->setParameter('id', $args['id'])
                    ->executeQuery()
                    ->fetchAssociative();
            }
        ]);

        $this->addQueryField(lcfirst($name) . 's', [
            'type' => Type::listOf($type),
            'resolve' => function($root, $args) use ($tableName) {
                $qb = $this->db->createQueryBuilder();
                return $qb->select('*')
                    ->from($tableName)
                    ->executeQuery()
                    ->fetchAllAssociative();
            }
        ]);

        return $type;
    }
}
