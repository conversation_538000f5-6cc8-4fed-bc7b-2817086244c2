<?php

namespace Zen\Core;

/**
 * Class Hash
 * Provides static methods for hashing and verifying passwords using bcrypt.
 */
class Hash
{
    /**
     * @var int Number of bcrypt rounds (cost).
     */
    protected static $rounds = 10;

    /**
     * Hash a value using bcrypt.
     * @param string $value
     * @param array $options
     * @return string
     * @throws \Exception
     */
    public static function make($value, array $options = [])
    {
        $hash = password_hash($value, PASSWORD_BCRYPT, [
            'cost' => self::cost($options),
        ]);
        if ($hash === false) {
            throw new \Exception('Bcrypt hashing not supported.');
        }
        return base64_encode($hash);
    }

    /**
     * Verify a value against a bcrypt hash.
     * @param string $value
     * @param string $hashedValue
     * @param array $options
     * @return bool
     */
    public static function verify($value, $hashedValue, array $options = [])
    {
        $hashedValue = base64_decode($hashedValue);
        if (strlen($hashedValue) === 0) {
            return false;
        }
        return password_verify($value, $hashedValue);
    }

    /**
     * Check if a hash needs to be rehashed with new options.
     * @param string $hashedValue
     * @param array $options
     * @return bool
     */
    public static function needsRehash($hashedValue, array $options = [])
    {
        return password_needs_rehash($hashedValue, PASSWORD_BCRYPT, [
            'cost' => self::cost($options),
        ]);
    }

    /**
     * Set the number of bcrypt rounds (cost).
     * @param int $rounds
     * @return string
     */
    public static function setRounds($rounds)
    {
        self::$rounds = (int) $rounds;
        return self::class;
    }

    /**
     * Get the cost value from options or use default.
     * @param array $options
     * @return int
     */
    protected static function cost(array $options = [])
    {
        return $options['rounds'] ?? self::$rounds;
    }
}
