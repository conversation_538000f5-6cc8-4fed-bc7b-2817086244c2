<?php

namespace Zen\Core\Http;

/**
 * Class Request
 * Handles incoming HTTP requests, providing access to request method, URI, headers, and input data.
 * It wraps Symfony's HttpFoundation Request component for robust request handling.
 */
class Request
{
    protected $method;
    protected $uri;
    protected $payload = null;
    protected $query = null;
    protected $headers = [];
    protected $rawRequest = null;

    /**
     * Request constructor.
     * Initializes the request method and URI from superglobals.
     */
    public function __construct()
    {
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->uri = $_SERVER['REQUEST_URI'];
    }

    /**
     * Initializes and returns the underlying Symfony Request object.
     * This method ensures the Symfony Request object is created only once.
     * @return \Symfony\Component\HttpFoundation\Request The Symfony Request object.
     */
    protected function init()
    {
        if (!is_null($this->rawRequest)) {
            return $this->rawRequest;
        }

        $this->rawRequest = \Symfony\Component\HttpFoundation\Request::createFromGlobals();
        return $this->rawRequest;
    }

    /**
     * Extracts data from a Symfony ParameterBag (e.g., query, request, headers).
     * @param \Symfony\Component\HttpFoundation\ParameterBag $data The ParameterBag instance.
     * @param string|null $key Optional. The specific key to retrieve. If null, all data is returned.
     * @return mixed The retrieved data or all data as an array.
     */
    protected function extractData($data, $key = null)
    {
        return !is_null($key) ? $data->get($key) : $data->all();
    }

    /**
     * Gets the request payload (POST data).
     * @return \Symfony\Component\HttpFoundation\ParameterBag The payload ParameterBag.
     */
    public function getPayload()
    {
        if (!is_null($this->payload)) {
            return $this->payload;
        }

        $this->payload = $this->init()->getPayload();
        return $this->payload;
    }

    /**
     * Gets the request query parameters (GET data).
     * @return \Symfony\Component\HttpFoundation\ParameterBag The query ParameterBag.
     */
    public function getQuery()
    {
        if (!is_null($this->query)) {
            return $this->query;
        }

        $this->query = $this->init()->query;
        return $this->query;
    }

    /**
     * Gets POST data by key or all POST data.
     * @param string|null $key Optional. The key to retrieve.
     * @return mixed The POST data.
     */
    public function post($key = null)
    {
        $data = $this->getPayload();
        return $this->extractData($data, $key);
    }

    /**
     * Gets GET data by key or all GET data.
     * @param string|null $key Optional. The key to retrieve.
     * @return mixed The GET data.
     */
    public function get($key = null)
    {
        $data = $this->getQuery();
        return $this->extractData($data, $key);
    }

    /**
     * Gets the HTTP request method (e.g., 'GET', 'POST').
     * @return string The request method.
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * Gets the full request URI.
     * @return string The request URI.
     */
    public function getUri()
    {
        return $this->uri;
    }

    /**
     * Gets the request path without query string.
     * @return string The request path.
     */
    public function getPath()
    {
        return explode('?', $this->getUri(), 2)[0];
    }

    /**
     * Gets the full URL of the current request.
     * @return string The full URL.
     */
    public function getUrl()
    {
        return url_origin() . $this->getUri();
    }

    /**
     * Gets the currently matched route information.
     * @return array|null The route information array or null if no route matched.
     */
    public function getRoute()
    {
        return \Zen\Core\Router::getCurrentRoute();
    }

    /**
     * Gets an HTTP header by key.
     * @param string $key The header name.
     * @param mixed $default Optional. The default value if the header is not found.
     * @return string|array|null The header value.
     */
    public function getHeader($key, $default = null)
    {
        return $this->extractData($this->init()->headers, $key) ?? $default;
    }

    /**
     * Gets a server parameter by key.
     * @param string $key The server parameter name.
     * @param mixed $default Optional. The default value if the parameter is not found.
     * @return string|null The server parameter value.
     */
    public function getServer($key, $default = null)
    {
        return $this->extractData($this->init()->server, $key) ?? $default;
    }

    /**
     * Gets an uploaded file by key.
     * @param string $key The file input name.
     * @param mixed $default Optional. The default value if the file is not found.
     * @return \Symfony\Component\HttpFoundation\File\UploadedFile|null The UploadedFile object.
     */
    public function getFile($key, $default = null)
    {
        return $this->extractData($this->init()->files, $key) ?? $default;
    }

    /**
     * Gets a cookie value by key.
     * @param string $key The cookie name.
     * @param mixed $default Optional. The default value if the cookie is not found.
     * @return string|null The cookie value.
     */
    public function getCookie($key, $default = null)
    {
        return $this->extractData($this->init()->cookies, $key) ?? $default;
    }

    /**
     * Gets a request attribute by key.
     * @param string $key The attribute name.
     * @param mixed $default Optional. The default value if the attribute is not found.
     * @return mixed The attribute value.
     */
    public function getAttribute($key, $default = null)
    {
        return $this->extractData($this->init()->attributes, $key) ?? $default;
    }

    /**
     * Checks if the request content type matches a given type.
     * @param string $type The content type to check against (e.g., 'json', 'text/html').
     * @return bool True if the content type matches, false otherwise.
     */
    public function isContentType($type)
    {
        return strpos($this->getHeader('Content-Type'), strip_tags($type)) !== false;
    }

    /**
     * Gets the raw request body content.
     * @return string The raw request body.
     */
    public function getContent()
    {
        return $this->init()->getContent();
    }

    /**
     * Get the HTTP range header
     * 
     * @return string|null The range header value or null if not present
     */
    public function getRange(): ?string
    {
        return $this->getHeader('HTTP_RANGE');
    }

    /**
     * Parse range request header
     * 
     * @param int $fileSize Total file size for validation
     * @return array{start: int, end: int, length: int, status: int}|false Returns range info or false if invalid
     */
    public function parseRange(int $fileSize)
    {
        $range = $this->getRange();
        if (!$range) {
            return [
                'start' => 0,
                'end' => $fileSize - 1,
                'length' => $fileSize,
                'status' => 200
            ];
        }

        // Remove 'bytes=' and get the range value
        list(, $range) = explode('=', $range, 2);
        
        // Multiple ranges not supported
        if (strpos($range, ',') !== false) {
            return false;
        }

        // Parse the range
        if (!preg_match('/(\d*)-(\d*)/', $range, $matches)) {
            return false;
        }

        $start = $matches[1] ? intval($matches[1]) : 0;
        $end = $matches[2] ? intval($matches[2]) : $fileSize - 1;

        // Validate range
        if ($start > $end || $start >= $fileSize || $end >= $fileSize) {
            return false;
        }

        return [
            'start' => $start,
            'end' => $end,
            'length' => $end - $start + 1,
            'status' => 206
        ];
    }
}
