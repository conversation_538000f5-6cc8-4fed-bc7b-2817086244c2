<?php

namespace Zen\Core\Http;

use Symfony\Component\HttpFoundation\Response as SymfonyResponse;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * Class Response
 * Handles outgoing HTTP responses, providing methods for setting status codes, headers, and content.
 * It wraps Symfony's HttpFoundation Response component for robust response handling.
 */
class Response
{
    protected $response;

    /**
     * Response constructor.
     * Initializes a new Symfony Response object.
     * @param string $content The response content.
     * @param int $statusCode The HTTP status code (default: 200).
     */
    public function __construct($content = '', $statusCode = 200)
    {
        $this->response = new SymfonyResponse($content, $statusCode);
    }

    /**
     * Sets the HTTP status code for the response.
     * @param int $statusCode The HTTP status code.
     * @return $this
     */
    public function setStatus($statusCode)
    {
        $this->response->setStatusCode($statusCode);
        return $this;
    }

    /**
     * Sets a single HTTP header for the response.
     * @param string $key The header name.
     * @param string $value The header value.
     * @return $this
     */
    public function setHeader($key, $value)
    {
        $this->response->headers->set($key, $value);
        return $this;
    }

    /**
     * Sets multiple HTTP headers for the response.
     * @param array $headers An associative array of header names and values.
     * @return $this
     */
    public function setHeaders($headers)
    {
        foreach ($headers as $key => $value) {
            $this->response->headers->set($key, $value);
        }
        return $this;
    }

    /**
     * Sets the content of the response.
     * @param string $content The response content.
     * @return $this
     */
    public function setContent($content)
    {
        $this->response->setContent($content);
        return $this;
    }

    /**
     * Sets a redirect response.
     * @param string $url The URL to redirect to.
     * @param int $statusCode The HTTP status code for the redirect (default: 302).
     * @return $this
     */
    public function setRedirect($url, $statusCode = 302)
    {
        $this->response = new SymfonyResponse('', $statusCode);
        $this->response->headers->set('Location', $url);
        return $this;
    }

    /**
     * Create a streamed response
     *
     * @param callable $callback A callable that will output the streamed content.
     * @param int $status The HTTP status code (default: 200).
     * @param array $headers Additional headers for the response.
     * @return $this
     */
    public function stream(callable $callback, int $status = 200, array $headers = [])
    {
        $this->response = new StreamedResponse($callback, $status, $headers);
        return $this;
    }

    /**
     * Create a binary file response.
     * This is suitable for serving files directly.
     * @param string $file The path to the file.
     * @param int $status The HTTP status code (default: 200).
     * @param array $headers Additional headers for the response.
     * @param bool $autoEtag Whether to automatically generate an ETag header (default: true).
     * @param bool $autoLastModified Whether to automatically generate a Last-Modified header (default: true).
     * @return $this
     */
    public function file(string $file, int $status = 200, array $headers = [], bool $autoEtag = true, bool $autoLastModified = true)
    {
        $this->response = new BinaryFileResponse($file, $status, $headers, true, ResponseHeaderBag::DISPOSITION_ATTACHMENT, $autoEtag, $autoLastModified);
        return $this;
    }

    /**
     * Sets the Content-Disposition header to 'inline', prompting the browser to display the file.
     * @param string|null $filename Optional. The filename to suggest for the inline display.
     * @return $this
     */
    public function setInline(?string $filename = null)
    {
        if ($this->response instanceof BinaryFileResponse) {
            $this->response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_INLINE,
                $filename . ''
            );
        }
        return $this;
    }

    /**
     * Sets the Content-Disposition header to 'attachment', prompting the browser to download the file.
     * @param string|null $filename Optional. The filename to suggest for the download.
     * @return $this
     */
    public function setAttachment(?string $filename = null)
    {
        if ($this->response instanceof BinaryFileResponse) {
            $this->response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                $filename
            );
        }
        return $this;
    }

    /**
     * Sends the HTTP response to the client.
     * @return void
     */
    public function send()
    {
        $this->response->send();
    }

    /**
     * Get the underlying Symfony response object.
     * @return SymfonyResponse
     */
    public function getSymfonyResponse()
    {
        return $this->response;
    }
}
