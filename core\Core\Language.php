<?php

namespace Zen\Core;

/**
 * Class Language
 * Handles loading and retrieving language strings for localization.
 */
class Language
{
    /**
     * @var array Language file paths for different sections.
     */
    protected static $languagePaths = [
        'global' => ZEN_PATH . '/resources/languages/',
        'theme' => ZEN_PATH . '/resources/themes/<type>/<name>/languages/',
        'plugin' => ZEN_PATH . '/resources/plugins/<name>/languages/',
    ];
    /**
     * @var string Default language code.
     */
    protected static $defaultLanguage = 'en';
    /**
     * @var string Default language file name.
     */
    protected static $defaultFilename = 'language.php';
    /**
     * @var array Cache for loaded language files.
     */
    protected static $cache = [];

    /**
     * Get language info from the default info file.
     * @return mixed|null
     */
    public static function meta($key = null)
    {
        $languageFolder = self::resolveLanguageRootPath('global');
        $meta = new \Zen\Core\Meta($languageFolder);
        $data = array_merge($meta->all(), [
            'path' => $languageFolder
        ]);

        if ($key !== null) {
            return $data[$key] ?? null;
        }

        return $data;
    }

    /**
     * Scan for available language files.
     * @return array List of language directories.
     */
    public static function scan()
    {
        $scanPath = self::$languagePaths['global'] . '*';
        return glob($scanPath, GLOB_ONLYDIR);
    }

    /**
     * Set the default language.
     * @param string $languageCode The language code to set as default.
     */
    public static function setDefaultLanguage($languageCode)
    {
        self::$defaultLanguage = $languageCode;
    }

    /**
     * Get a language value by key.
     * @param string $langKey The language key to retrieve.
     * @param mixed $defaultValue The default value to return if the key is not found.
     * @param string $section The section to look in (global, theme, plugin).
     * @return mixed The language value or the default value.
     */
    public static function get($langKey, $defaultValue = null, $section = "global")
    {
        $resolveKey = self::resolveLanguageKey($langKey);
        $filePath = self::resolveLanguageRootPath($section) . self::resolveFilePath($resolveKey[0]);
        $key = $resolveKey[1] ?? '';
        $langValue = false;

        if (!isset(self::$cache[$filePath])) {
            if (file_exists($filePath)) {
                self::$cache[$filePath] = include $filePath;
            }
        }

        if (isset(self::$cache[$filePath])) {
            $langValue = isset(self::$cache[$filePath][$key]) ? self::$cache[$filePath][$key] : null;
        }

        if ($section != 'global' && (is_null($langValue) || $langValue == false)) {
            $langValue = self::get($langKey);
        }

        return $langValue ? $langValue : $defaultValue;
    }

    /**
     * Resolve the file path for a language file.
     * @param string $file The file name or path to resolve.
     * @return string The resolved file path.
     */
    protected static function resolveFilePath($file)
    {
        // Default to the default language folder if no specific folder is provided
        if (empty($file)) {
            $file = self::$defaultFilename;
        }

        // Check if it's a file or folder and adjust accordingly
        if (strpos($file, '/') !== false) {
            return $file;
        }

        return $file;
    }

    /**
     * Resolve the root path for language files of a given section.
     * @param string $section The section to resolve the path for.
     * @return string The resolved root path for language files.
     */
    protected static function resolveLanguageRootPath($section = 'global')
    {
        $rootPath = self::$languagePaths['global'];

        $sectionParts = explode(':', $section, 3);
        switch (strtolower($sectionParts[0])) {
            case 'theme':
                $rootPath = str_replace(
                    ['<type>', '<name>'],
                    [$sectionParts[1], $sectionParts[2]],
                    self::$languagePaths['theme']
                );
                break;

            case 'plugin':
                $rootPath = str_replace(
                    '<name>',
                    $sectionParts[1],
                    self::$languagePaths['plugin']
                );
                break;

            default:
                $rootPath = self::$languagePaths['global'];
                break;
        }

        return $rootPath . self::$defaultLanguage . '/';
    }

    /**
     * Resolve the file and key from a language key.
     * @param string $key The language key to resolve.
     * @return array An array containing the resolved file path and key.
     */
    protected static function resolveLanguageKey($key)
    {
        $parts = explode('/', $key);
        $lastPart = array_pop($parts); // Get the last part of the path

        // Handle different scenarios
        if (strpos($lastPart, ':') !== false) {
            // Last part contains a colon (e.g., hello:anykey)
            $fileName = explode(':', $lastPart)[0] . '.php';
            $key = explode(':', $lastPart)[1];
        } else {
            // Last part doesn't contain a colon (e.g., anykey)
            $fileName = self::$defaultFilename;
            $key = $lastPart;
        }

        // Reconstruct the path
        $filePath = implode('/', $parts);
        $filePath = $filePath ? $filePath . '/' . $fileName : $fileName;

        return [$filePath, $key];
    }
}
