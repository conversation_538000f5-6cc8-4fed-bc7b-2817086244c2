<?php

namespace Zen\Core;

/**
 * Class Layer
 * Provides a mechanism for registering and running named layers (middleware-like logic).
 */
class Layer
{
    /**
     * @var array Registered layers.
     */
    protected static $layers = [];

    /**
     * Register a layer callback.
     * @param string $name
     * @param callable $callback
     * @return void
     */
    public static function set($name, $callback)
    {
        self::$layers[$name] = $callback;
    }

    /**
     * Call a registered layer by name.
     * @param string $layerName
     * @return bool|null
     */
    public static function call($layerName)
    {
        if (isset(self::$layers[$layerName])) {
            return call_user_func(self::$layers[$layerName]) === true ? true : false;
        }
    }

    /**
     * List all registered layers.
     * @return array
     */
    public static function list()
    {
        return self::$layers;
    }

    /**
     * Check if a layer exists by name.
     * @param string $layerName
     * @return bool
     */
    public static function exists($layerName)
    {
        return isset(self::$layers[$layerName]);
    }
    
    /**
     * Register and immediately run a layer.
     * @param string $name
     * @param callable $callback
     * @return bool|null
     */
    public static function run($name, $callback)
    {
        self::set($name, $callback);
        return self::call($name);
    }

    /**
     * Generate a unique layer name based on the route name and index.
     * @param string $routeName
     * @param int $index
     * @return string
     */
    public static function generateLayerName($routeName, $index)
    {
        $routesList = \Zen\Core\Router::list();
        $layerName = $routeName . '_layer_' . $index;

        if(rtrim($routeName, \Zen\Core\Router::$secondaryRouteSuffix) == $routeName) {
            foreach($routesList[$routeName][3] as $routeLayer) {                
                if($routeLayer == $layerName) {
                    $layerName = $layerName . "_" . $index;
                }
            }
        }

        return $layerName;
    }
}
