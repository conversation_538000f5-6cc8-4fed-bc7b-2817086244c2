<?php

namespace Zen\Core;

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;

/**
 * Class Mail
 * Handles sending emails using P<PERSON>Mailer with application settings.
 */
class Mail
{
    /**
     * @var PHPMailer PHPMailer instance.
     */
    private PHPMailer $mailer;
    /**
     * @var Mail|null Singleton instance.
     */
    private static ?Mail $instance = null;

    /**
     * Mail constructor. Initializes PHPMailer.
     */
    private function __construct()
    {
        $this->mailer = new PHPMailer(true);
        $this->initializeMailer();
    }

    /**
     * Get Mail instance (Singleton pattern)
     * @return Mail
     */
    public static function getInstance(): Mail
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize PHPMailer with default settings
     * @return void
     * @throws \Exception
     */
    private function initializeMailer(): void
    {
        try {
            $this->mailer->isSMTP();
            $this->mailer->Host = Settings::get('mail.host', 'smtp.gmail.com');
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = Settings::get('mail.username', '');
            $this->mailer->Password = Settings::get('mail.password', '');
            $this->mailer->SMTPSecure = Settings::get('mail.encryption', PHPMailer::ENCRYPTION_STARTTLS);
            $this->mailer->Port = Settings::get('mail.port', 587);
            $this->mailer->CharSet = 'UTF-8';
            $this->mailer->setFrom(
                Settings::get('mail.from.address', ''),
                Settings::get('mail.from.name', '')
            );
        } catch (Exception $e) {
            throw new \Exception("Mail configuration error: " . $e->getMessage());
        }
    }

    /**
     * Send an email
     * @param string|array $to Recipient email address(es)
     * @param string $subject Email subject
     * @param string $body Email body (HTML)
     * @param string|null $plainText Plain text version of the email
     * @param array $attachments Array of file paths to attach
     * @return bool
     * @throws \Exception
     */
    public function send($to, string $subject, string | array $body, ?string $plainText = null, array $attachments = []): bool
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();

            // Add recipients
            if (is_array($to)) {
                foreach ($to as $recipient) {
                    $this->mailer->addAddress($recipient);
                }
            } else {
                $this->mailer->addAddress($to);
            }

            $this->mailer->Subject = $subject;
            $this->mailer->isHTML(true);
            if(is_array($body)) {
                $template_file = $body[0];
                $template_params = $body[1] ?? [];
                $renderedBody = Theme::render($template_file, $template_params, 'email');
                $this->mailer->Body = $renderedBody;
            } else {
                $this->mailer->Body = $body;
            }

            if ($plainText !== null) {
                $this->mailer->AltBody = $plainText;
            }

            // Add attachments
            foreach ($attachments as $attachment) {
                if (file_exists($attachment)) {
                    $this->mailer->addAttachment($attachment);
                }
            }

            return $this->mailer->send();
        } catch (Exception $e) {
            throw new \Exception("Failed to send email: " . $e->getMessage());
        }
    }

    /**
     * Add CC recipient
     * @param string $address
     * @param string $name
     * @return Mail
     */
    public function addCC(string $address, string $name = ''): self
    {
        try {
            $this->mailer->addCC($address, $name);
            return $this;
        } catch (Exception $e) {
            throw new \Exception("Failed to add CC: " . $e->getMessage());
        }
    }

    /**
     * Add BCC recipient
     * @param string $address
     * @param string $name
     * @return Mail
     */
    public function addBCC(string $address, string $name = ''): self
    {
        try {
            $this->mailer->addBCC($address, $name);
            return $this;
        } catch (Exception $e) {
            throw new \Exception("Failed to add BCC: " . $e->getMessage());
        }
    }

    /**
     * Add Reply-To address
     * @param string $address
     * @param string $name
     * @return Mail
     */
    public function addReplyTo(string $address, string $name = ''): self
    {
        try {
            $this->mailer->addReplyTo($address, $name);
            return $this;
        } catch (Exception $e) {
            throw new \Exception("Failed to add Reply-To: " . $e->getMessage());
        }
    }
}
