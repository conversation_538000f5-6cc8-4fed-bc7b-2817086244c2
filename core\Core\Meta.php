<?php

namespace Zen\Core;

/**
 * Class Meta
 * Handles reading and validating metadata for components like themes, plugins, etc.
 */
class Meta
{
    protected string $path;
    protected array $data = [];
    protected string $currentMeta = '';

    public function __construct(string $directory, string $filename = 'meta.json')
    {
        $this->path = rtrim($directory, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . $filename;

        if (!file_exists($this->path)) {
            throw new \Exception("Meta file not found: {$this->path}");
        }

        $this->currentMeta = md5($this->path);
        if (isset($this->data[$this->currentMeta])) {
            return; // already loaded
        }

        $json = \Zen\Core\File::read($this->path);
        if ($json === false) {
            throw new \Exception("Failed to read meta file: {$this->path}");
        }

        // Validate the JSON structure
        if (!self::isValidMeta($json)) {
            throw new \Exception("Invalid or malformed meta file: {$this->path}");
        }

        // Save the decoded data
        $this->data[$this->currentMeta] = json_decode($json, true);
    }

    public function get(string $key, $default = null)
    {
        return $this->data[$this->currentMeta][$key] ?? $default;
    }

    public function all(): array
    {
        return $this->data[$this->currentMeta] ?? [];
    }

    /**
     * Validate that the meta file contains required fields and valid type.
     */
    public static function isValidMeta(string $file_content): bool
    {
        $data = json_decode($file_content, true);

        if (json_last_error() !== JSON_ERROR_NONE || !is_array($data)) {
            return false;
        }

        // removed author and description 
        $required = ['type', 'id', 'name', 'version'];

        foreach ($required as $key) {
            if (empty($data[$key]) || !is_string($data[$key])) {
                return false;
            }
        }

        $allowedTypes = ['web', 'email', 'admin', 'plugin', 'language'];

        if (!in_array($data['type'], $allowedTypes, true)) {
            return false;
        }

        return true;
    }
}
