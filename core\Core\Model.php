<?php

namespace Zen\Core;

use Doctrine\DBAL\Query\QueryBuilder;

/**
 * Abstract base model class for ORM-like database interaction.
 */
abstract class Model
{
    /**
     * @var mixed Database connection instance.
     */
    protected static $connection = null;
    /**
     * @var string Table name for the model.
     */
    protected static $table;
    /**
     * @var array Model attributes.
     */
    protected $attributes = [];
    /**
     * @var array Original attributes (for tracking changes).
     */
    protected $original = [];

    /**
     * @var Pagination|null Pagination instance for the model.
     * This is used to handle paginated results.
     */
    protected $pagination = null;

    /**
     * Link the model to a database connection if not already linked.
     * @return void
     */
    public static function linkConnection()
    {
        if(is_null(static::$connection)) {
            static::$connection = \Zen\Core\Db::dbal();
        }
    }

    /**
     * Model constructor. Optionally fill attributes.
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        $this->fill($attributes);
    }

    /**
     * Fill the model attributes with the given array.
     * @param array $attributes
     * @return void
     */
    public function fill(array $attributes)
    {
        foreach ($attributes as $key => $value) {
            $this->setAttribute($key, $value);
        }
    }

    /**
     * Set a single attribute for the model.
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function setAttribute($key, $value)
    {
        $this->attributes[$key] = $value;
    }

    /**
     * Get a single attribute value by key.
     * @param string $key
     * @return mixed
     */
    public function getAttribute($key)
    {
        return $this->attributes[$key] ?? null;
    }

    /**
     * Magic method to get attribute value.
     * @param string $key
     * @return mixed
     */
    public function __get($key)
    {
        return $this->getAttribute($key);
    }

    /**
     * Magic method to set attribute value.
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function __set($key, $value)
    {
        $this->setAttribute($key, $value);
    }

    /**
     * Get the table name for the model, using the class name as a fallback.
     * @return string
     */
    public static function getTable()
    {
        return static::$table ?? strtolower((new \ReflectionClass(static::class))->getShortName()) . 's';
    }

    /**
     * Create a new query builder instance for the model.
     * @return QueryBuilder
     */
    public static function query(): QueryBuilder
    {
        static::linkConnection();

        return static::$connection->createQueryBuilder()
            ->from(static::getTable());
    }

    /**
     * Retrieve all records from the table.
     * @param string|array $params
     * @return array
     */
    public static function all($params = '*'): array
    {
        $result = static::query()->select($params)->executeQuery()->fetchAllAssociative();
        return array_map(function ($item) {
            return new static($item);
        }, $result);
    }

    /**
     * Find a record by its primary key or a set of attributes.
     * @param mixed $param
     * @param string|array $select
     * @return static|null
     */
    public static function find($param, $select = '*')
    {
        $query = static::query()->select($select);

        if (is_array($param)) {
            // Build the where clause dynamically for each key-value pair in the array
            foreach ($param as $key => $value) {
                $query->andWhere("$key = :$key")
                    ->setParameter($key, $value);
            }
        } else {
            // Use the = clause for single values
            $query->where('id = :id')
                ->setParameter('id', $param);
        }

        $result = $query->executeQuery()->fetchAssociative();

        return $result ? new static($result) : null;
    }


    /**
     * Add a where clause to the query.
     * @param string $column
     * @param string $operator
     * @param mixed $value
     * @return QueryBuilder
     */
    public static function where($column, $operator, $value = null): QueryBuilder
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }

        return static::query()
            ->where("$column $operator :value")
            ->setParameter('value', $value);
    }

    public static function paginate(int $perPage = 15, int $currentPage = 1): Pagination
    {
        static::linkConnection();
    
        $pagination = new Pagination(static::query(), $perPage, $currentPage);

        return $pagination;
    }
    
    /**
     * Save the model to the database (insert or update).
     * @return bool
     */
    public function save(): bool
    {
        if (isset($this->attributes['id'])) {
            return $this->update();
        }

        return $this->insert();
    }

    /**
     * Insert a new record into the database.
     * @return bool
     */
    protected function insert(): bool
    {
        static::linkConnection();

        $queryBuilder = static::$connection->createQueryBuilder();
        $queryBuilder->insert(static::getTable());

        foreach ($this->attributes as $column => $value) {
            $queryBuilder->setValue($column, ":$column")
                         ->setParameter($column, $value);
        }

        $result = $queryBuilder->executeStatement();

        if ($result) {
            $this->attributes['id'] = static::$connection->lastInsertId();
        }

        return $result > 0;
    }

    /**
     * Update an existing record in the database.
     * @return bool
     */
    protected function update(): bool
    {
        static::linkConnection();

        $queryBuilder = static::$connection->createQueryBuilder();
        $queryBuilder->update(static::getTable())
                     ->where('id = :id')
                     ->setParameter('id', $this->attributes['id']);

        foreach ($this->attributes as $column => $value) {
            if ($column !== 'id') {
                $queryBuilder->set($column, ":$column")
                             ->setParameter($column, $value);
            }
        }

        $result = $queryBuilder->executeStatement();
        return $result > 0;
    }

    /**
     * Delete the record from the database.
     * @return bool
     */
    public function delete(): bool
    {
        static::linkConnection();

        if (!isset($this->attributes['id'])) {
            return false;
        }

        $result = static::$connection->createQueryBuilder()
            ->delete(static::getTable())
            ->where('id = :id')
            ->setParameter('id', $this->attributes['id'])
            ->executeStatement();

        return $result > 0;
    }

}
