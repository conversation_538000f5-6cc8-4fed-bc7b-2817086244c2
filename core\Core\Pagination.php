<?php

namespace Zen\Core;

use Doctrine\DBAL\Query\QueryBuilder;
use Twig\Node\Expression\Test\NullTest;

/**
 * Class Pagination
 * Handles paginated results for database queries.
 */
class Pagination
{
    /**
     * @var QueryBuilder Query builder instance.
     */
    protected QueryBuilder $query;
    /**
     * @var int Current page number.
     */
    protected int $currentPage;
    /**
     * @var int Number of items per page.
     */
    protected int $perPage;
    /**
     * @var int Total number of items.
     */
    protected int $total;
    /**
     * @var array Items for the current page.
     */
    protected array $items = [];
    /**
     * @var bool Enable performance mode (skip total count).
     */
    protected bool $performanceMode = false;
    /**
     * @var string Default query parameter for page.
     */
    private string $defaultPageQuery = 'page';

    /**
     * @var array Links for pagination.
     */
    private array $links = [];

    private array $defaultLinksPattern = [
        'container' => '<ul>:loop:</ul>',
        'item' => '<li><a href=":url:">:label:</a></li>',
        'active' => '<li class="active"><a href="javascript:void(0);">:label:</a></li>',
        'disabled' => '<li class="disabled"><span>:label:</span></li>',
        'prev' => '<li><a href=":url:">&laquo;</a></li>',
        'next' => '<li><a href=":url:">&raquo;</a></li>',
        'prev_disabled' => '<li class="disabled"><span>&laquo;</span></li>',
        'next_disabled' => '<li class="disabled"><span>&raquo;</span></li>'
    ];
    private string | null $defaultLinksView = 'pagination';

    /**
     * Pagination constructor.
     * @param QueryBuilder $query
     * @param int $currentPage
     * @param int $perPage
     */
    public function __construct(QueryBuilder $query, int $perPage = 15, int $currentPage = 1)
    {
        $this->query = $query;
        $this->currentPage = max(1, $currentPage);
        $this->perPage = max(1, $perPage);

        return $this;
    }

    /**
     * Enable performance mode to skip total count calculation.
     * @return self
     */
    public function performanceMode(): self
    {
        $this->performanceMode = true;
        return $this;
    }

    /**
     * Fetch the paginated items.
     * @param string $select
     * @return self
     */
    public function fetch($select = '*'): self
    {
        if (!$this->performanceMode) {
            $this->total = $this->calculateTotal();
        }

        $offset = ($this->currentPage - 1) * $this->perPage;
        $result = $this->query
            ->select($select)
            ->setFirstResult($offset)
            ->setMaxResults($this->perPage + 1) // Fetch one extra item to determine if there are more pages
            ->executeQuery();

        $this->items = $result->fetchAllAssociative();

        // Trim the extra item if fetched
        if (count($this->items) > $this->perPage) {
            array_pop($this->items);
        }

        return $this;
    }

    /**
     * Calculate the total number of items.
     * @return int
     */
    protected function calculateTotal(): int
    {
        $countQuery = $this->createCountQueryBuilder($this->query);
        $result = $countQuery->executeQuery();
        return (int) $result->fetchOne();
    }

    /**
     * Create a count query builder for the total number of items.
     * @param QueryBuilder $queryBuilder
     * @return QueryBuilder
     */
    protected function createCountQueryBuilder(QueryBuilder $queryBuilder): QueryBuilder
    {
        return $queryBuilder
            ->select('COUNT(*)')
            ->from('(' . $queryBuilder->getSQL() . ')', 'tmp')
            ->setParameters($queryBuilder->getParameters(), $queryBuilder->getParameterTypes());
    }

    /**
     * Get the items for the current page.
     * @return array
     */
    public function getItems(): array
    {
        return $this->items;
    }

    /**
     * Get the total number of pages.
     * @return int
     */
    public function getTotalPages(): int
    {
        return $this->performanceMode ? -1 : ceil($this->total / $this->perPage);
    }

    /**
     * Get the current page number.
     * @return int
     */
    public function getCurrentPage(): int
    {
        return $this->currentPage;
    }

    /**
     * Get the number of items per page.
     * @return int
     */
    public function getPerPage(): int
    {
        return $this->perPage;
    }

    /**
     * Get the total number of items.
     * @return int
     */
    public function getTotal(): int
    {
        return $this->performanceMode ? -1 : $this->total;
    }

    /**
     * Check if there are more pages.
     * @return bool
     */
    public function hasMorePages(): bool
    {
        if ($this->performanceMode) {
            return count($this->items) === $this->perPage;
        }
        return $this->currentPage < $this->getTotalPages();
    }

    /**
     * Get the URL for the previous page.
     * @param bool $returnOnly
     * @return string|null
     */
    public function previousPageUrl($returnOnly = false): ?string
    {
        if ($this->currentPage > 1) {
            if($returnOnly) {
                return $this->currentPage - 1;
            }
            return $this->buildPageUrl($this->currentPage - 1);
        }
        return null;
    }

    /**
     * Get the URL for the next page.
     * @param bool $returnOnly
     * @return string|null
     */
    public function nextPageUrl($returnOnly = false): ?string
    {
        if ($this->hasMorePages()) {
            if($returnOnly) {
                return $this->currentPage + 1;
            }
            return $this->buildPageUrl($this->currentPage + 1);
        }
        return null;
    }

    /**
     * Set the query parameter name for the page.
     * @param string $pageParam
     * @return self
     */
    public function setPageParam(string $pageParam, $forceCurrentPage = false): self
    {
        $this->defaultPageQuery = $pageParam;

        if($forceCurrentPage) {
            $this->currentPage = isset($_GET[$this->defaultPageQuery]) ? (int)$_GET[$this->defaultPageQuery] : 1;
            $this->currentPage = max(1, $this->currentPage); // Ensure
        }

        return $this;
    }

    /**
     * Build the URL for a given page.
     * @param int $page
     * @return string
     */
    protected function buildPageUrl(int $page): string
    {
        $params = $_GET;
        $params[$this->defaultPageQuery] = $page;
        return '?' . http_build_query($params);
    }

    /**
     * Build the pagination links array.
     * @param array $links
     * @return array
     */
    protected function buildLinks(array $links): array
    {
        return [
            "prev" => [
                'page' => $this->previousPageUrl(true),
                'url' => $this->previousPageUrl(),
                'label' => 'prev',
                'active' => false
            ],
            "links" => $links,
            "next" => [
                'page' => $this->nextPageUrl(true),
                'url' => $this->nextPageUrl(),
                'label' => 'next',
                'active' => false
            ]
        ];
    }

    /**
     * Generate the pagination links.
     * @param int $onEachSide
     * @return array
     */
    public function links(int $onEachSide = 3): array
    {
        if ($this->performanceMode) {
            $links = [];
            if ($this->currentPage > 1) {
                if(($this->currentPage - 1) != 1) {
                    $links[] = [
                        'page' => null,
                        'url' => null,
                        'label' => '...',
                        'active' => false
                    ];
                }
                $links[] = [
                    'page' => $this->currentPage - 1,
                    'url' => $this->previousPageUrl(),
                    'label' => (string)($this->currentPage - 1),
                    'active' => ($this->currentPage - 1) === $this->currentPage
                ];
            }
            $links[] = [
                'page' => $this->currentPage,
                'url' => $this->buildPageUrl($this->currentPage),
                'label' => (string)$this->currentPage,
                'active' => true
            ];
            if ($this->hasMorePages()) {
                $links[] = [
                    'page' => $this->currentPage + 1,
                    'url' => $this->nextPageUrl(),
                    'label' => (string)($this->currentPage + 1),
                    'active' => ($this->currentPage + 1) === $this->currentPage
                ];
                $links[] = [
                    'page' => null,
                    'url' => null,
                    'label' => '...',
                    'active' => false
                ];
            }

            $this->links = $this->buildLinks($links);

            return $this->links;
        }

        $links = [];
        $totalPages = $this->getTotalPages();
        $start = max(1, $this->currentPage - $onEachSide);
        $end = min($totalPages, $this->currentPage + $onEachSide);

        if ($start > 1) {
            $links[] = ['page' => 1, 'url' => $this->buildPageUrl(1), 'label' => '1'];
            if ($start > 2) {
                $links[] = ['page' => null, 'url' => null, 'label' => '...'];
            }
        }

        for ($page = $start; $page <= $end; $page++) {
            $links[] = [
                'page' => $page,
                'url' => $this->buildPageUrl($page),
                'label' => (string)$page,
                'active' => $page === $this->currentPage
            ];
        }

        if ($end < $totalPages) {
            if ($end < $totalPages - 1) {
                $links[] = ['page' => null, 'url' => null, 'label' => '...', 'active' => false];
            }
            $links[] = [
                'page' => $totalPages,
                'url' => $this->buildPageUrl($totalPages),
                'label' => (string)$totalPages,
                'active' => $page === $this->currentPage
            ];
        }

        $this->links = $this->buildLinks($links);

        return $this->links;
    }

    /** 
     * Set a custom pattern for pagination links.
     * @param array $pattern
     * @return self
     */
    public function setPaginationPattern($pattern = []): self
    {
        $this->defaultLinksPattern = array_merge($this->defaultLinksPattern, $pattern);
        return $this;
    }

    /** 
     * Set a custom pattern for pagination links.
     * @param array $pattern
     * @return self
     */
    public function setPaginationView($view = null): self
    {
        $this->defaultLinksView = $view;
        return $this;
    }

    /**
     * Render the pagination links as HTML.
     * @param array $pattern
     * @return string
     */
    public function renderPattern($pattern = [])
    {
        $links = $this->links;
        if (empty($links)) {
            $links = $this->links();

            if (empty($links)) {
                return '';
            }
        }

        $pattern = array_merge($this->defaultLinksPattern, $pattern);

        $linksHTML = '';

        // Previous page link
        if (!empty($links['prev']['url'])) {
            $linksHTML .= str_replace(':url:', $links['prev']['url'], $pattern['prev']);
        } else {
            $linksHTML .= $pattern['prev_disabled'];
        }

        // Page number links
        foreach ($links['links'] as $link) {
            if (!empty($link['active'])) {
                $linksHTML .= str_replace(':label:', $link['label'], $pattern['active']);
            } elseif (!empty($link['url'])) {
                $linksHTML .= str_replace(
                    [':url:', ':label:'],
                    [$link['url'], $link['label']],
                    $pattern['item']
                );
            } else {
                // For separators like '...'
                $linksHTML .= str_replace(':label:', $link['label'], $pattern['disabled']);
            }
        }

        // Next page link
        if (!empty($links['next']['url'])) {
            $linksHTML .= str_replace(':url:', $links['next']['url'], $pattern['next']);
        } else {
            $linksHTML .= $pattern['next_disabled'];
        }

        return str_replace(':loop:', $linksHTML, $pattern['container']);
    }

    /**
     * Render the pagination links as HTML.
     * @param array $pattern
     * @return string
     */
    public function render($view = null)
    {
        $view = $view ?: $this->defaultLinksView;
        $links = $this->links;
        if (empty($links)) {
            $links = $this->links();

            if (empty($links)) {
                return '';
            }
        }

        if($view) {
            return \Zen\Core\Theme::render($view, [
                'links' => $links
            ]);
        }

        return '';
    }
}
