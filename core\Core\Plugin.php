<?php

namespace Zen\Core;

class Plugin
{
    private static ?object $instance = null;
    protected static array $plugins = [];
    protected static string $pluginAssetsBasePath = '/plugin-assets/';
    protected static string $basePath = '/resources/plugins/';

    public function __construct()
    {
        $this->loadAll();
    }

    /**
     * Get plugin info from the default info file.
     * @return mixed|null
     */
    public static function meta($pluginFolderPath, $key = null)
    {
        $meta = new \Zen\Core\Meta($pluginFolderPath);
        $data = $meta->all();
        $data = array_merge($meta->all(), [
            'path' => $pluginFolderPath
        ]);

        if ($key !== null) {
            return $data[$key] ?? null;
        }

        return $data;
    }

    public static function scan()
    {
        $scanPath = ZEN_PATH . self::$basePath . '*';
        return glob($scanPath, GLOB_ONLYDIR);
    }

    public static function getInstance(): object
    {
        if (static::$instance === null) {
            static::$instance = new static();
        }

        return static::$instance;
    }

    public function loadAll(): void
    {
        $plugins = self::scan();
        foreach ($plugins as $plugin) {
            if(self::meta($plugin)) {
                $this->load($plugin);
            }
        }
    }

    public function load(string $path): bool
    {
        $config = $path . '/plugin.json';
        if (!file_exists($config)) {
            return false;
        }
        $config = json_decode(file_get_contents($config), true);
        if (empty($config['id'])) {
            return false;
        }
        static::$plugins[$config['id']] = $config;
        static::$plugins[$config['id']]['path'] = $path;
        static::$plugins[$config['id']]['url'] = url_origin() . self::$pluginAssetsBasePath . basename($path);
        return true;
    }

    public function list(): array
    {
        return static::$plugins;
    }

    public function get(string $id): ?array
    {
        return static::$plugins[$id] ?? null;
    }

    public function isActive(string $id): bool
    {
        $settings = \Zen\Core\Settings::get('plugins');
        if (empty($settings)) {
            return false;
        }
        return in_array($id, $settings);
    }

    public function init(): void
    {
        foreach (static::$plugins as $plugin) {
            if ($this->isActive($plugin['id'])) {
                $file = $plugin['path'] . '/init.php';
                if (file_exists($file)) {
                    require_once $file;
                }
            }
        }
    }
}
