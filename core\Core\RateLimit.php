<?php

namespace Core;

use Zen\Core\Session;

/**
 * Class RateLimit
 * Provides static methods for rate limiting actions (e.g., login attempts).
 */
class RateLimit
{
    /**
     * @var string Prefix for rate limit session keys.
     */
    private static string $prefix = 'rate_limit_';
    /**
     * @var string Prefix for rate limit attempt session keys.
     */
    private static string $attemptPrefix = 'rate_limit_attempt_';

    /**
     * Check if the rate limit has been exceeded.
     * @param string $key Unique identifier for the rate limit
     * @param int $maxAttempts Maximum number of attempts allowed
     * @param int $decayMinutes Number of minutes until the rate limit resets
     * @return bool Returns true if rate limit is exceeded
     */
    public static function tooManyAttempts(string $key, int $maxAttempts, int $decayMinutes = 1): bool
    {
        if (self::attempts($key) >= $maxAttempts) {
            if (self::availableIn($key) > 0) {
                return true;
            }

            self::resetAttempts($key);
        }

        return false;
    }

    /**
     * Increment the counter for a given key
     *
     * @param string $key Unique identifier for the rate limit
     * @param int $decayMinutes Number of minutes until the rate limit resets
     * @return int Returns the current number of attempts
     */
    public static function hit(string $key, int $decayMinutes = 1): int
    {
        $key = self::$prefix . $key;

        Session::start();

        $expiration = time() + ($decayMinutes * 60);

        if (!Session::has($key)) {
            Session::set($key, $expiration);
            Session::set(self::$attemptPrefix . $key, 0);
        }

        Session::set(self::$attemptPrefix . $key, self::attempts($key) + 1);

        return self::attempts($key);
    }

    /**
     * Get the number of attempts for the given key
     *
     * @param string $key Unique identifier for the rate limit
     * @return int Returns the current number of attempts
     */
    public static function attempts(string $key): int
    {
        return (int) Session::get(self::$attemptPrefix . self::$prefix . $key, 0);
    }

    /**
     * Reset the number of attempts for the given key
     *
     * @param string $key Unique identifier for the rate limit
     * @return void
     */
    public static function resetAttempts(string $key): void
    {
        $key = self::$prefix . $key;
        Session::remove($key);
        Session::remove(self::$attemptPrefix . $key);
    }

    /**
     * Get the number of seconds remaining until the rate limit resets
     *
     * @param string $key Unique identifier for the rate limit
     * @return int Returns the number of seconds remaining
     */
    public static function availableIn(string $key): int
    {
        $key = self::$prefix . $key;
        $expiration = Session::get($key, 0);

        return max(0, $expiration - time());
    }

    /**
     * Determine if the given key has been "accessed too many times"
     *
     * @param string $key Unique identifier for the rate limit
     * @param int $maxAttempts Maximum number of attempts allowed
     * @param int $decayMinutes Number of minutes until the rate limit resets
     * @return array Returns an array with rate limit information
     */
    public static function check(string $key, int $maxAttempts, int $decayMinutes = 1): array
    {
        if (!self::tooManyAttempts($key, $maxAttempts, $decayMinutes)) {
            self::hit($key, $decayMinutes);
        }

        return [
            'attempts' => self::attempts($key),
            'remaining' => max(0, $maxAttempts - self::attempts($key)),
            'exceeds' => self::tooManyAttempts($key, $maxAttempts, $decayMinutes),
            'available_in' => self::availableIn($key)
        ];
    }

    /**
     * Clear all rate limits
     *
     * @return void
     */
    public static function clear(): void
    {
        Session::start();
        foreach ($_SESSION as $key => $value) {
            if (str_starts_with($key, self::$prefix) || str_starts_with($key, self::$attemptPrefix)) {
                Session::remove($key);
            }
        }
    }
}
