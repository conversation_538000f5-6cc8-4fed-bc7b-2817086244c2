<?php

namespace Zen\Core;

use Zen\Core\Error;

class Router
{
    protected static $routes = array();
    protected static $routePrefix = '';
    protected static $middleware = [];
    protected static $namedRoutes = array();
    protected static $basePath = '';
    protected static $matchTypes = array(
        'i'  => '[0-9]++',
        'a'  => '[0-9A-Za-z]++',
        'h'  => '[0-9A-Fa-f]++',
        '*'  => '.+?',
        '**' => '.++',
        ''   => '[^/\.]++'
    );

    public static $alias = array();
    public static $secondaryRouteSuffix = '_secondary';

    protected static $currentRoute = null;

    public static function addRoutes($routes)
    {
        if (!is_array($routes)) {
            Error::print('Routes should be an array');
        }
        foreach ($routes as $route) {
            call_user_func_array(array(self::class, 'addRoute'), $route);
        }
    }

    public static function setBasePath($basePath)
    {
        self::$basePath = $basePath;
    }

    public static function addMatchTypes($matchTypes)
    {
        self::$matchTypes = array_merge(self::$matchTypes, $matchTypes);
    }

    public static function group($prefix, $callback)
    {
        $previousPrefix = self::$routePrefix;
        self::$routePrefix .= $prefix;

        // Call the callback function to define routes within the group
        call_user_func($callback);

        // Reset the route prefix to its previous value after defining routes in the group
        self::$routePrefix = $previousPrefix;
    }

    public static function layer($name, $callback)
    {
        \Zen\Core\Layer::set($name, $callback);
    }

    public static function secondaryRouteName($name)
    {
        return $name . self::$secondaryRouteSuffix;
    }

    public static function set($method, $route, $target, $name = '', $layers = [], $slash = true)
    {
        preg_match('/\w{1,}/', $route, $m);
        if (!empty($m[0])) {
            self::$alias[$m[0]] = $m[0];
        }

        $route = self::$routePrefix . $route;
        self::$routes[$name] = array($method, $route, $target, $layers, $name);
        if ($slash) {
            self::$routes[self::secondaryRouteName($name)] = array($method, $route."/", $target, $layers, self::secondaryRouteName($name));
        }

        if ($name) {
            if (isset(self::$namedRoutes[$name])) {
                Error::print("Duplicated route '{$name}'");
            } else {
                self::$namedRoutes[$name] = $route;
            }
        }

        return new self();
    }

    public static function generate($routeName, array $params = array())
    {

        // Check if named route exists
        if (!isset(self::$namedRoutes[$routeName])) {
            Error::print("Route '{$routeName}' does not exist.");
        }

        // Replace named parameters
        $route = self::$namedRoutes[$routeName];

        // prepend base path to route url again
        $url = self::$basePath . $route;

        if (preg_match_all('`(/|\.|)\[([^:\]]*+)(?::([^:\]]*+))?\](\?|)`', $route, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                list($block, $pre, $type, $param, $optional) = $match;

                if ($pre) {
                    $block = substr($block, 1);
                }

                if (isset($params[$param])) {
                    $url = str_replace($block, $params[$param], $url);
                } elseif ($optional) {
                    $url = str_replace($pre . $block, '', $url);
                }
            }
        }
        return $url;
    }

    public static function route($requestUrl = null, $requestMethod = null)
    {
        $params = array();
        $match = false;

        // set Request Url if it isn't passed as parameter
        if ($requestUrl === null) {
            $requestUrl = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '/';
        }

        // strip base path from request url
        $requestUrl = substr($requestUrl, strlen(self::$basePath));

        // Strip query string (?a=b) from Request Url
        if (($strpos = strpos($requestUrl, '?')) !== false) {
            $requestUrl = substr($requestUrl, 0, $strpos);
        }

        // set Request Method if it isn't passed as a parameter
        if ($requestMethod === null) {
            $requestMethod = isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'GET';
        }

        foreach (self::$routes as $handler) {
            list($method, $_route, $target, $layers, $name) = $handler;

            $methods = explode('|', $method);
            $methodMatch = false;

            // Check if request method matches. If not, abandon early. (CHEAP)
            foreach ($methods as $method) {
                if (strcasecmp($requestMethod, $method) === 0) {
                    $methodMatch = true;
                    break;
                }
            }

            // Method did not match, continue to next route.
            if (!$methodMatch) {
                continue;
            }

            // Check for a wildcard (matches all)
            if ($_route === '*') {
                $match = true;
            } elseif (isset($_route[0]) && $_route[0] === '::') {
                $pattern = '`' . substr($_route, 1) . '`u';
                $match = preg_match($pattern, $requestUrl, $params);
            } else {
                $route = null;
                $regex = false;

                $positionInRequestUrl = 0;
                $nextRoutePart = isset($_route[0]) ? $_route[0] : null;
                $routeIndex = 0;

                // Find the longest non-regex substring and match it against the URI
                while (true) {
                    if (!isset($_route[$routeIndex])) {
                        break;
                    } elseif (false === $regex) {
                        $currentChar  = $nextRoutePart;
                        $regex = $currentChar  === '[' || $currentChar  === '(' || $currentChar  === '.' || $currentChar === '?';
                        if (false === $regex && false !== isset($_route[$routeIndex + 1])) {
                            $nextRoutePart = $_route[$routeIndex + 1];
                            $regex = $nextRoutePart === '?' || $nextRoutePart === '+' || $nextRoutePart === '*' || $nextRoutePart === '{';
                        }
                        if (false === $regex && $currentChar  !== '/' && (!isset($requestUrl[$positionInRequestUrl]) || $currentChar  !== $requestUrl[$positionInRequestUrl])) {
                            continue 2;
                        }
                        $positionInRequestUrl++;
                    }
                    $route .= $_route[$routeIndex++];
                }

                $regex = self::compileRoute($route);
                $match = preg_match($regex, $requestUrl, $params);
            }

            if (($match == true || $match > 0)) {
                if ($params) {
                    foreach ($params as $key => $value) {
                        if (is_numeric($key)) {
                            unset($params[$key]);
                        }
                    }
                }

                return array(
                    'name' => $name,
                    'params' => $params,
                    'handler' => $target,
                    'layers' => $layers
                );
            }
        }
        return false;
    }

    public static function generateUrl($routeName, array $params = array(), $pathOnly = false)
    {
        // Check if named route exists
        if (!isset(self::$routes[$routeName])) {
            throw new \Exception('Route not found');
        }

        // prepend base path to route url again
        $urlPath = self::$routes[$routeName][1];

        if (preg_match_all('`(/|\.|)\[([^:\]]*+)(?::([^:\]]*+))?\](\?|)`', $routeName, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                list($block, $pre, $type, $param, $optional) = $match;

                if ($pre) {
                    $block = substr($block, 1);
                }

                if (isset($params[$param])) {
                    $urlPath = str_replace($block, $params[$param], $urlPath);
                } elseif ($optional) {
                    $urlPath = str_replace($pre . $block, '', $urlPath);
                }
            }
        }

        return $matches;

        return $pathOnly ? $urlPath : url_origin() . $urlPath;
    }

    public static function call($matchedRoute = [])
    {
        if ($matchedRoute !== null && !empty($matchedRoute)) {

            $checkMiddleware = true;

            // Apply layers
            foreach ($matchedRoute['layers'] as $middlewareKey => $middleware) {

                if($checkMiddleware){
                    // check if its string
                    if(is_string($middleware)) {
                        $checkMiddleware = \Zen\Core\Layer::call($middleware);
                    } elseif(is_callable($middleware)) {
                        $checkMiddleware = \Zen\Core\Layer::run(
                            \Zen\Core\Layer::generateLayerName($matchedRoute['name'], $middlewareKey),
                            $middleware
                        );
                    }
                } else {
                    return;
                }
            }

            if(!$checkMiddleware) {
                return;
            }

            self::$currentRoute = $matchedRoute;

            if (is_callable($matchedRoute['handler'])) {
                call_user_func($matchedRoute['handler'], $matchedRoute['params']);
            } elseif (is_array($matchedRoute['handler'])) {
                // Handle array format [Controller::class, 'method', ['arg1', 'arg2']]
                $controller = $matchedRoute['handler'][0];
                $method = $matchedRoute['handler'][1];
                $args = isset($matchedRoute['handler'][2]) ? $matchedRoute['handler'][2] : [];

                // Merge route params with predefined args
                if (!empty($matchedRoute['params'])) {
                    $args = array_merge($args, $matchedRoute['params']);
                }

                $controllerInstance = new $controller();
                call_user_func_array([$controllerInstance, $method], $args);
            } else {
                // Handle string format "Controller::method"
                $parts = explode('::', trim($matchedRoute['handler']));
                $controller = $parts[0];
                $method = $parts[1];
                $controllerInstance = new $controller();
                $controllerInstance->$method($matchedRoute['params']);
            }
        } else {
            // Handle 404 Not Found
            Error::notFound();
        }
    }

    private static function compileRoute($route)
    {
        if (preg_match_all('`(/|\.|)\[([^:\]]*+)(?::([^:\]]*+))?\](\?|)`', !is_null($route) ? $route : "", $matches, PREG_SET_ORDER)) {
            $matchTypes = self::$matchTypes;
            foreach ($matches as $match) {
                list($block, $pre, $type, $param, $optional) = $match;

                if (isset($matchTypes[$type])) {
                    $type = $matchTypes[$type];
                }
                if ($pre === '.') {
                    $pre = '\.';
                }

                //Older versions of PCRE require the 'P' in (?P<named>)
                $pattern = '(?:'
                        . ($pre !== '' ? $pre : null)
                        . '('
                        . ($param !== '' ? "?P<$param>" : null)
                        . $type
                        . '))'
                        . ($optional !== '' ? '?' : null);

                $route = str_replace($block, $pattern, $route);
            }
        }
        return "`^$route$`u";
    }

    public static function list()
    {
        return self::$routes;
    }

    public static function exists($routerName)
    {
        return isset(self::$routes[$routerName]);
    }

    public static function getCurrentRoute()
    {
        return self::$currentRoute;
    }
}
