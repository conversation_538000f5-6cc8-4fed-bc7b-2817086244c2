<?php

namespace Zen\Core;

/**
 * Class Session
 * Provides static methods for session management (start, set, get, flash, etc.).
 */
class Session
{
    /**
     * Start the session if not already started.
     * @return void
     */
    public static function start(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * Set a session value.
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public static function set(string $key, mixed $value): void
    {
        self::start();
        $_SESSION[$key] = $value;
    }

    /**
     * Get a session value.
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        self::start();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if a session key exists.
     * @param string $key
     * @return bool
     */
    public static function has(string $key): bool
    {
        self::start();
        return isset($_SESSION[$key]);
    }

    /**
     * Remove a session key.
     * @param string $key
     * @return void
     */
    public static function remove(string $key): void
    {
        self::start();
        unset($_SESSION[$key]);
    }

    /**
     * Get and remove a session value.
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function pull(string $key, mixed $default = null): mixed
    {
        $value = self::get($key, $default);
        self::remove($key);
        return $value;
    }

    /**
     * Clear all session data and destroy the session.
     * @return void
     */
    public static function clear(): void
    {
        self::start();
        session_unset();
        session_destroy();
    }

    /**
     * Flash a message to the session (for one-time use).
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public static function flash(string $key, mixed $value): void
    {
        self::set('_flash_' . $key, $value);
    }

    /**
     * Get a flashed message (and remove it).
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getFlash(string $key, mixed $default = null): mixed
    {
        return self::pull('_flash_' . $key, $default);
    }

    /**
     * Regenerate the session ID for security.
     * @return void
     */
    public static function regenerate(): void
    {
        self::start();
        session_regenerate_id(true);
    }
}
