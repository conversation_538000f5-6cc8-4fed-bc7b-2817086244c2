<?php

namespace Zen\Core;

use Zen\Core\Storage;

/**
 * Class Settings
 * Handles loading, caching, and backing up application settings.
 */
class Settings
{
    /**
     * @var array Settings cache.
     */
    private static $cache = [];
    /**
     * @var bool Enable/disable settings cache.
     */
    private static $cacheEnabled = true;

    /**
     * Load a settings map.
     * @param string $section The section name (e.g., 'custom').
     * @return array The settings map.
     * @throws \Exception
     */
    public static function loadMap(string $section): array
    {
        if (self::$cacheEnabled && isset(self::$cache['map'][$section])) {
            return self::$cache['map'][$section];
        }
        $mapFile = Storage::$settingsMapsPath . '/' . $section . '.map.php';
        if (!file_exists($mapFile)) {
            throw new \Exception("Settings map for section '$section' not found.");
        }
        try {
            $map = include $mapFile;
        } catch (\ParseError $e) {
            throw new \Exception("Could not parse the map file for section '$section'.");
        }
        self::$cache['map'][$section] = self::applyLangProcessing($map);
        return $map;
    }

    /**
     * Backup settings for a given section.
     *
     * @param string $section The section name (e.g., 'custom').
     * @return bool True on success, false if no data found to backup.
     */
    public static function backupSettings(string $section)
    {
        $data = self::loadData($section);
        if($data){
            $dataFile = Storage::$settingsDataPath . '/' . $section . '.data.php';
            $backupDataFile = Storage::$settingsBackupPath . '/' . $section . '.backup.php';
            $fileContent = Storage::get($dataFile);

            // save
            Storage::put($backupDataFile, $fileContent);
            return true;
        }
        return false;
    }

    /**
     * Restore settings for a given section from backup.
     *
     * @param string $section The section name (e.g., 'custom').
     * @return bool True on success, false if no backup found.
     */
    public static function restoreSettings(string $section)
    {
        $dataFile = Storage::$settingsDataPath . '/' . $section . '.data.php';
        $backupDataFile = Storage::$settingsBackupPath . '/' . $section . '.backup.php';
        $fileContent = Storage::get($backupDataFile);
        Storage::put($dataFile, $fileContent);
        return true;
    }
    
    /**
     * Load settings data.
     *
     * @param string $section The section name (e.g., 'custom').
     * @return array The settings data.
     * @throws \Exception
     */
    public static function loadData(string $section)
    {
        if (self::$cacheEnabled && isset(self::$cache['data'][$section])) {
            return self::$cache['data'][$section];
        }

        $dataFile = Storage::$settingsDataPath . '/' . $section . '.data.php';

        if (!file_exists($dataFile)) {
            return false;
        }

        try {
            $serializedData = include $dataFile;
        } catch (\ParseError $e) {
            throw new \Exception("Could not parse the setting file for section '$section'.");
        }

        if (!is_string($serializedData)) {
            throw new \Exception("Settings data for section '$section' is not a valid serialized string.");
        }

        $data = unserialize(stripslashes($serializedData));
        if(self::$cacheEnabled){
            self::$cache['data'][$section] = $data;
        }

        return $data;
    }

    /**
     * Save settings data.
     *
     * @param string $section The section name (e.g., 'custom').
     * @param array $data The data to save.
     */
    public static function saveData(string $section, array $data): void
    {
        $dataFile = Storage::$settingsDataPath . '/' . $section . '.data.php';
        $serializedData = serialize($data);
        $content = "<?php return '" . addslashes($serializedData) . "';";

        // backup previous data
        self::backupSettings($section);

        // save new data
        Storage::put($dataFile, $content);

        self::$cache['data'][$section] = $data;
    }

    /**
     * Delete a section's settings.
     *
     * @param string $section The section name.
     */
    public static function deleteSection(string $section): void
    {
        $mapFile = Storage::$settingsMapsPath . '/' . $section . '.map.php';
        $dataFile = Storage::$settingsDataPath . '/' . $section . '.data.php';

        // delete the files
        Storage::delete($mapFile);
        Storage::delete($dataFile);

        unset(self::$cache['map'][$section], self::$cache['data'][$section]);
    }

    /**
     * Edit a setting within a section.
     *
     * @param string $section The section name.
     * @param string $key The setting key.
     * @param mixed $value The value to set.
     * @throws \Exception If validation fails
     */
    public static function update(string $section, string $key, $value): void
    {
        // Load map and data
        $map = self::loadMap($section);
        $data = self::loadData($section);

        // Check if field exists in map
        if (!isset($map['fields'][$key])) {
            throw new \Exception("Setting '$key' does not exist in section '$section'");
        }

        $field = $map['fields'][$key];

        // Check if field is editable
        if (empty($field['is_editable'])) {
            throw new \Exception("Setting '$key' is not editable");
        }

        // Validate and format value based on field type
        $value = self::validateAndFormatValue($value, $field);

        // Update data
        $data[$key] = $value;

        // Save changes
        self::saveData($section, $data);
    }

    /**
     * Validate and format a value based on field configuration
     *
     * @param mixed $value The value to validate
     * @param array $field The field configuration
     * @return mixed The formatted value
     * @throws \Exception If validation fails
     */
    private static function validateAndFormatValue($value, array $field): mixed
    {
        // Handle required fields
        if (!empty($field['is_required']) && 
            ($value === null || $value === '' || (is_array($value) && empty($value)))) {
            throw new \Exception("Field '{$field['label']}' is required");
        }

        // Handle multiple values
        if (!empty($field['is_multiple'])) {
            if (!is_array($value)) {
                $value = [$value];
            }
        } else {
            if (is_array($value)) {
                // If multiple values provided for single field, take first value
                $value = reset($value);
            }
        }

        // Validate based on input type
        switch ($field['input_type']) {
            case 'toggle':
                // Convert boolean-like values to 'on'/'off'
                if (is_bool($value)) {
                    $value = $value ? 'on' : 'off';
                } elseif (is_string($value)) {
                    $value = strtolower($value);
                    if (!in_array($value, ['on', 'off'])) {
                        throw new \Exception("Toggle value must be 'on' or 'off'");
                    }
                } else {
                    throw new \Exception("Invalid toggle value");
                }
                break;

            case 'number':
                if (!is_numeric($value)) {
                    throw new \Exception("Value must be numeric");
                }
                $value = (float)$value;
                break;

            case 'select':
            case 'radio':
            case 'checkbox':
                if (!empty($field['options'])) {
                    $validValues = array_column($field['options'], 'value');
                    if (!empty($field['is_multiple'])) {
                        foreach ($value as $val) {
                            if (!in_array($val, $validValues)) {
                                throw new \Exception("Invalid option value: $val");
                            }
                        }
                    } else {
                        if (!in_array($value, $validValues)) {
                            throw new \Exception("Invalid option value: $value");
                        }
                    }
                }
                break;
        }

        return $value;
    }

    /**
     * Get setting value with type conversion
     *
     * @param string $key The setting key in dot notation (section.key)
     * @param mixed $default Default value if setting not found
     * @return mixed The setting value
     */
    public static function get(string $key, $default = null)
    {
        // Split the key into section and field
        $parts = explode('.', $key);
        if (count($parts) !== 2) {
            return self::loadData($key);
        }

        [$section, $field] = $parts;

        try {
            $data = self::loadData($section);
            if (!$data || !isset($data[$field])) {
                return $default;
            }

            $value = $data[$field];

            // Try to load map for type conversion
            try {
                $map = self::loadMap($section);
                if (isset($map['fields'][$field])) {
                    $fieldConfig = $map['fields'][$field];
                    
                    // Convert toggle values to boolean if needed
                    if ($fieldConfig['input_type'] === 'toggle') {
                        return $value === 'on';
                    }
                    
                    // Convert number values
                    if ($fieldConfig['input_type'] === 'number') {
                        return is_numeric($value) ? (float)$value : $value;
                    }
                }
            } catch (\Exception $e) {
                // If map not found, return raw value
            }

            return $value;
        } catch (\Exception $e) {
            return $default;
        }
    }

    /**
     * Import settings from an external source (e.g., JSON).
     *
     * @param string $section The section name.
     * @param array $importData The data to import.
     */
    public static function importSettings(string $section, array $importData): void
    {
        $data = self::loadData($section);
        $data = array_merge($data, $importData);

        self::saveData($section, $data);
    }

    /**
     * Enable or disable caching.
     *
     * @param bool $enabled True to enable caching, false to disable.
     */
    public static function setCacheEnabled(bool $enabled): void
    {
        self::$cacheEnabled = $enabled;
    }

    /**
     * Process language keys in the format "lang:key:default".
     *
     * @param string $value The string containing the language key.
     * @return string The translated or default string.
     */
    public static function processLangKey(string $value): string
    {
        if (strpos($value, 'lang:') === 0) {
            $parts = explode(':', $value, 3);

            if (count($parts) === 3) {
                $langKey = $parts[1];
                $defaultText = $parts[2];
                return \Zen\Core\Language::get($langKey, $defaultText, 'global');
            }

            if (count($parts) === 2) {
                $langKey = $parts[1];
                return \Zen\Core\Language::get($langKey, '', 'global');
            }
        }

        return $value;
    }

    /**
     * Apply language processing to a settings map or data.
     *
     * @param array $settings The settings array.
     * @return array The settings array with language keys processed.
     */
    public static function applyLangProcessing(array $settings): array
    {
        foreach ($settings as $key => $value) {
            if (is_string($value)) {
                $settings[$key] = self::processLangKey($value);
            } elseif (is_array($value)) {
                $settings[$key] = self::applyLangProcessing($value);
            }
        }

        return $settings;
    }

    /**
     * Create a new settings section builder
     * 
     * @param string $section Section name
     * @param string $name Display name
     * @return Setup
     */
    public static function create(string $section, string $name): Setup
    {
        return new Setup($section, $name);
    }
}

/**
 * Settings Setup Helper Class
 */
class Setup
{
    private string $section;
    private array $map;
    private array $data = [];

    public function __construct(string $section, string $name)
    {
        $this->section = $section;
        $this->map = [
            'name' => $name,
            'description' => '',
            'scope' => 'system',
            'scope_area' => '',
            'fields' => []
        ];
    }

    /**
     * Set section description
     */
    public function description(string $description): self
    {
        $this->map['description'] = $description;
        return $this;
    }

    /**
     * Set section scope and optionally scope_area
     */
    public function scope(string $scope, ?string $scopeArea = null): self
    {
        $this->map['scope'] = $scope;
        if ($scopeArea !== null) {
            $this->map['scope_area'] = $scopeArea;
        } else {
            $this->map['scope_area'] = $this->section;
        }
        return $this;
    }

    /**
     * Set scope area directly
     */
    public function scopeArea(string $area): self
    {
        $this->map['scope_area'] = $area;
        return $this;
    }

    /**
     * Add a text field
     */
    public function addText(string $name, string $label, array $options = []): self
    {
        return $this->addField($name, 'text', $label, $options);
    }

    /**
     * Add a textarea field
     */
    public function addTextarea(string $name, string $label, array $options = []): self
    {
        return $this->addField($name, 'textarea', $label, $options);
    }

    /**
     * Add a number field
     */
    public function addNumber(string $name, string $label, array $options = []): self
    {
        return $this->addField($name, 'number', $label, $options);
    }

    /**
     * Add a toggle field
     */
    public function addToggle(string $name, string $label, bool $default = false, array $options = []): self
    {
        $options['default'] = $default ? 'on' : 'off';
        return $this->addField($name, 'toggle', $label, $options);
    }

    /**
     * Add a select field
     */
    public function addSelect(string $name, string $label, array $choices, array $options = []): self
    {
        $options['options'] = array_map(function($value, $text) {
            return ['value' => $value, 'text' => $text];
        }, array_keys($choices), array_values($choices));
        
        return $this->addField($name, 'select', $label, $options);
    }

    /**
     * Add a radio field
     */
    public function addRadio(string $name, string $label, array $choices, array $options = []): self
    {
        $options['options'] = array_map(function($value, $text) {
            return ['value' => $value, 'text' => $text];
        }, array_keys($choices), array_values($choices));
        
        return $this->addField($name, 'radio', $label, $options);
    }

    /**
     * Add a checkbox field
     */
    public function addCheckbox(string $name, string $label, array $choices, array $options = []): self
    {
        $options['is_multiple'] = true;
        $options['options'] = array_map(function($value, $text) {
            return ['value' => $value, 'text' => $text];
        }, array_keys($choices), array_values($choices));
        
        return $this->addField($name, 'checkbox', $label, $options);
    }

    /**
     * Add initial data
     */
    public function withData(array $data): self
    {
        $this->data = array_merge($this->data, $data);
        return $this;
    }

    /**
     * Save the settings configuration
     */
    public function save()
    {
        // Create map file
        $mapFile = Storage::$settingsMapsPath . '/' . $this->section . '.map.php';
        $mapContent = "<?php\n\nreturn [\n";
        $mapContent .= "    'name' => " . $this->exportValue($this->map['name']) . ",\n";
        $mapContent .= "    'description' => " . $this->exportValue($this->map['description']) . ",\n";
        $mapContent .= "    'scope' => " . $this->exportValue($this->map['scope']) . ",\n";
        $mapContent .= "    'scope_area' => " . $this->exportValue($this->map['scope_area']) . ",\n";
        $mapContent .= "    'fields' => [\n";
        
        foreach ($this->map['fields'] as $name => $field) {
            $mapContent .= "        '$name' => [\n";
            foreach ($field as $key => $value) {
                $mapContent .= "            '$key' => " . $this->exportValue($value) . ",\n";
            }
            $mapContent .= "        ],\n";
        }
        
        $mapContent .= "    ]\n];";

        if (!Storage::put($mapFile, $mapContent)) {
            throw new \Exception("Failed to create map file for section '{$this->section}'");
        }

        // Generate data from defaults and provided data
        $finalData = [];
        foreach ($this->map['fields'] as $name => $field) {
            if (isset($this->data[$name])) {
                $finalData[$name] = $this->data[$name];
            } elseif (isset($field['default'])) {
                if (!empty($field['is_multiple']) && !is_array($field['default'])) {
                    $finalData[$name] = [$field['default']];
                } else {
                    $finalData[$name] = $field['default'];
                }
            }
        }

        // Save data file
        return Settings::saveData($this->section, $finalData);
    }

    /**
     * Add a field with common options
     */
    private function addField(string $name, string $type, string $label, array $options = []): self
    {
        $field = array_merge([
            'is_visible' => true,
            'is_editable' => true,
            'is_required' => false,
            'is_multiple' => false,
            'input_type' => $type,
            'label' => $label,
            'placeholder' => '',
            'description' => '',
            'default' => null
        ], $options);

        $this->map['fields'][$name] = $field;
        return $this;
    }

    /**
     * Export a value as PHP code
     */
    private function exportValue($value): string
    {
        if (is_array($value)) {
            $output = "[\n";
            foreach ($value as $k => $v) {
                $key = is_string($k) ? "'$k'" : $k;
                $output .= "                $key => " . $this->exportValue($v) . ",\n";
            }
            return $output . "            ]";
        }
        if (is_string($value)) return "'$value'";
        if (is_bool($value)) return $value ? 'true' : 'false';
        if (is_null($value)) return 'null';
        return $value;
    }
}
