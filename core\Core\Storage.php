<?php

namespace Zen\Core;

/**
 * Class Storage
 * Handles file storage paths and directory initialization for the application.
 */
class Storage
{
    /**
     * @var string Base storage path.
     */
    public static string $storagePath = ZEN_PATH . '/storage';
    /**
     * @var string Public storage path.
     */
    public static string $storagePublicPath = ZEN_PATH . '/storage/public';
    /**
     * @var string Cache storage path.
     */
    public static string $cachePath = ZEN_PATH . '/storage/secured/cache';
    /**
     * @var string Logs storage path.
     */
    public static string $logsPath = ZEN_PATH . '/storage/secured/logs';
    /**
     * @var string Secure storage path.
     */
    public static string $securePath = ZEN_PATH . '/storage/secured/setup';
    /**
     * @var string Settings storage path.
     */
    public static string $settingsPath = ZEN_PATH . '/storage/secured/settings';
    /**
     * @var string Settings maps path.
     */
    public static string $settingsMapsPath = ZEN_PATH . '/storage/secured/settings/maps';
    /**
     * @var string Settings data path.
     */
    public static string $settingsDataPath = ZEN_PATH . '/storage/secured/settings/data';
    /**
     * @var string Settings backup path.
     */
    public static string $settingsBackupPath = ZEN_PATH . '/storage/secured/settings/backup';

    /**
     * Initialize storage directories.
     * @return void
     */
    public static function init(): void
    {
        self::ensureDirectoriesExist([
            self::$storagePath,
            self::$cachePath,
            self::$logsPath,
            self::$securePath,
            self::$settingsPath,
            self::$settingsMapsPath,
            self::$settingsDataPath,
            self::$settingsBackupPath
        ]);
    }

    /**
     * Ensure all required directories exist
     *
     * @param array $paths
     */
    public static function ensureDirectoriesExist(array $paths): void
    {
        foreach ($paths as $path) {
            if (!file_exists($path)) {
                mkdir($path, 0755, true);
            }
        }
    }

    /**
     * Get contents of a file
     * 
     * @param string $path File path
     * @param int|null $offset Starting position
     * @param int|null $length Number of bytes to read
     * @param bool $stream Whether to use stream reading for large files
     * @return string|false Returns false if file doesn't exist
     */
    public static function get(string $path, ?int $offset = null, ?int $length = null, bool $stream = false)
    {
        if (!file_exists($path)) {
            return false;
        }

        // For small files or when streaming isn't needed
        if (!$stream) {
            if ($offset === null && $length === null) {
                // Fast read for entire file
                return file_get_contents($path);
            }
            
            // Partial read with offset/length
            return file_get_contents($path, false, null, $offset ?? 0, $length ?? -1);
        }

        // Stream reading for large files
        $handle = fopen($path, 'rb');
        if ($handle === false) {
            return false;
        }

        if ($offset !== null) {
            fseek($handle, $offset);
        }

        if ($length === null) {
            $data = stream_get_contents($handle);
        } else {
            $data = stream_get_contents($handle, $length);
        }

        fclose($handle);
        return $data;
    }

    /**
     * Put contents into a file
     *
     * @param string $path
     * @param string $contents
     * @return bool
     */
    public static function put(string $path, string $contents): bool
    {
        $directory = dirname($path);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        return file_put_contents($path, $contents) !== false;
    }

    /**
     * Append contents to a file
     *
     * @param string $path
     * @param string $contents
     * @return bool
     */
    public static function append(string $path, string $contents): bool
    {
        $directory = dirname($path);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }
        return file_put_contents($path, $contents, FILE_APPEND) !== false;
    }

    /**
     * Delete a file
     *
     * @param string $path
     * @return bool
     */
    public static function delete(string $path): bool
    {
        return file_exists($path) ? unlink($path) : false;
    }

    /**
     * Check if a file exists
     *
     * @param string $path
     * @return bool
     */
    public static function exists(string $path): bool
    {
        $realPath = realpath($path);

        // If realpath fails, the file does not exist
        if ($realPath === false) {
            return false;
        }

        // Compare the basename of the realpath with the provided basename
        return basename($realPath) === basename($path) && is_file($realPath);
    }

    /**
     * Get file size in bytes
     *
     * @param string $path
     * @return int|false
     */
    public static function size(string $path)
    {
        return file_exists($path) ? filesize($path) : false;
    }

    /**
     * Get the last modified time of a file
     *
     * @param string $path
     * @return int|false
     */
    public static function lastModified(string $path)
    {
        return file_exists($path) ? filemtime($path) : false;
    }

    /**
     * Clear a directory
     *
     * @param string $directory
     * @return bool
     */
    public static function clearDirectory(string $directory): bool
    {
        if (!file_exists($directory)) {
            return false;
        }

        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($files as $file) {
            if ($file->isDir()) {
                rmdir($file->getRealPath());
            } else {
                unlink($file->getRealPath());
            }
        }

        return true;
    }

    /**
     * Stream a file with support for HTTP range requests
     * 
     * @param string $path File path
     * @param \Zen\Core\Http\Request $request Request object
     * @param callable $callback Callback function to handle headers and streaming
     * @return array|false Returns array with stream info or false if file doesn't exist
     */
    public static function stream(string $path, \Zen\Core\Http\Request $request, callable $callback)
    {
        if (!file_exists($path)) {
            return false;
        }

        $size = filesize($path);
        $range = $request->parseRange($size);

        if ($range === false) {
            return [
                'status' => 416,
                'headers' => [
                    'Content-Range' => 'bytes */' . $size
                ]
            ];
        }

        // Call the callback with headers
        $callback([
            'status' => $range['status'],
            'headers' => [
                'Content-Type' => mime_content_type($path),
                'Content-Length' => $range['length'],
                'Accept-Ranges' => 'bytes',
                'Content-Range' => $range['status'] === 206 ? "bytes {$range['start']}-{$range['end']}/$size" : null,
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]
        ]);

        // Stream the file
        $handle = fopen($path, 'rb');
        if ($handle === false) {
            return false;
        }

        if ($range['start'] > 0) {
            fseek($handle, $range['start']);
        }

        $remaining = $range['length'];
        $bufferSize = 8192;

        while (!feof($handle) && $remaining > 0) {
            $chunkSize = min($bufferSize, $remaining);
            $data = fread($handle, $chunkSize);
            echo $data;
            flush();
            $remaining -= strlen($data);
        }

        fclose($handle);
        return true;
    }
}
