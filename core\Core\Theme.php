<?php

namespace Zen\Core;


/**
 * Class Theme
 * Handles theme selection, rendering, and asset management for different areas (web, admin, email).
 */
class Theme
{
    /**
     * @var array Theme paths for each area.
     */
    protected static $themePaths = [
        'web' => ZEN_PATH . '/resources/themes/web/',
        'admin' => ZEN_PATH . '/resources/themes/admin/',
        'email' => ZEN_PATH . '/resources/themes/email/'
    ];

    /**
     * @var array Theme asset paths for each area.
     */
    protected static $themeAssetsPath = [
        'web' => '/assets/',
        'admin' => '/admin-assets/',
        'email' => '/email-assets/'
    ];

    /**
     * @var array Current theme name.
     */
    protected static $theme = [
        'web' => 'default',
        'admin' => 'default',
        'email' => 'default'
    ];

    /**
     * @var string Current area (web, admin, email).
     */
    protected static $currentArea = 'web';

    /**
     * @var array View parameters for rendering.
     */
    protected static $viewParams = [];
    /**
     * @var bool Enable/disable minification.
     */
    protected static $minify = false;
    /**
     * @var array List of allowed functions for templates.
     */
    protected static $allowedFunctions = [
        'strlen',
        'md5'
    ];

    /**
     * Set the current theme.
     * @param string $theme
     * @return void
     */
    public static function setTheme($area, $theme)
    {
        if(in_array($area, array_keys(self::$theme))){
            self::$theme[$area] = $theme;
        }
    }
    
    /**
     * Get the current theme.
     * @return string
     */
    public static function getCurrentTheme($area = false)
    {
        return self::$theme[$area ?? self::$currentArea];
    }

    /**
     * Set the current area.
     * @param string $area
     * @return void
     */
    public static function setArea($area = 'web')
    {
        self::$currentArea = in_array($area, array_keys(self::$themePaths)) ? $area : 'web';
    }

    /**
     * Get the theme path for the current area.
     * @return string
     */
    public static function getThemePath($area = false)
    {
        return self::$themePaths[$area ?: self::$currentArea];
    }

    /**
     * Get the full theme path, including the theme name.
     * @return string
     */
    public static function getFullThemePath($area = false)
    {
        if(empty(self::$theme)) {
            self::$theme[$area ?? self::$currentArea] = \Zen\Core\Zen::loadConfig('defaults')['template'][self::$currentArea];
        }

        return self::getThemePath($area) . self::$theme[$area ?? self::$currentArea] . '/';
    }

    /**
     * Set the minification option.
     * @param bool $minify
     * @return void
     */
    public static function setMinify($minify = true)
    {
        self::$minify = $minify;
    }

    /**
     * Link allowed functions for use in templates.
     * @return \Devknown\Twig\Extension\ImportFunctionExtension
     */
    public static function link_functions()
    {
        $extension = new \Devknown\Twig\Extension\ImportFunctionExtension();
        $extension->import(self::$allowedFunctions);
        return $extension;
    }

    /**
     * Render a view template with the given parameters.
     * @param string $view
     * @param array $params
     * @return string
     * @throws \Exception
     */
    public static function render($view, $params = [], $area = 'web')
    {
        $globalEnv = \Zen\Core\Zen::loadConfig('environment') ?? 'development';
        $themePath = self::getFullThemePath($area);
        $viewFile = $themePath . $view . '.html';

        $functionsFile = $themePath . '/functions.php';
        if (is_file($functionsFile)) {
            try {
                $themeFunctions = require_once($functionsFile);
                if(!empty($themeFunctions) && is_array($themeFunctions)) {
                    self::$allowedFunctions = array_merge(self::$allowedFunctions, $themeFunctions);
                }
            } catch (\ParseError $e) {
                throw new \Exception("Could not parse the functions for the current theme.");
            }
        }

        if (is_file($viewFile)) {

            if (!empty($params)) {
                self::$viewParams[$view] = array_merge(self::$viewParams[$view] ?? [], $params);
            }

            $loader = new \Twig\Loader\FilesystemLoader($themePath);
            if (strtolower($globalEnv) == "development") {
                $twig = new \Twig\Environment($loader, array());
            } else {
                $twig = new \Twig\Environment($loader, array(
                    'cache' => Storage::$cachePath . '/' . self::getThemePath(),
                ));
            }

            $extension = self::link_functions();
            $twig->addExtension($extension);

            return $twig->render($view . '.html', self::$viewParams[$view] ?? []);
        } else {
            if (strtolower($globalEnv) == "development") {
                $blankFile = $themePath . '/blank.html';
                file_put_contents($viewFile, is_file($blankFile) ? file_get_contents($blankFile) : '');
                Error::print("a new file created: " . $viewFile);
            } else {
                Error::print("Please specify a valid template view: " . $viewFile);
            }
        }

    }

    /**
     * Load an asset (CSS or JS) for the current theme.
     * @param string $asset
     * @param string $type
     * @return string
     * @throws \InvalidArgumentException
     */
    public static function loadAsset($asset, $type = 'css')
    {
        $url = self::getThemeUrl($asset);

        switch ($type) {
            case 'css':
                return '<link href="' . $url . '" rel="stylesheet" type="text/css" />';
            case 'js':
                return '<script src="' . $url . '" type="text/javascript"></script>';
            default:
                throw new \InvalidArgumentException('Invalid asset type: ' . $type . ', file: ' . $asset);
        }
    }

    /**
     * Add a meta tag to the document head.
     * @param string $name
     * @param string $content
     * @return string
     */
    public static function addMeta($name, $content)
    {
        $name = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
        $content = htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
        return '<meta name="' . $name . '" content="' . $content . '" />';
    }

    /**
     * Add an Open Graph meta tag to the document head.
     * @param string $property
     * @param string $content
     * @return string
     */
    public static function addOpenGraph($property, $content)
    {
        $property = htmlspecialchars($property, ENT_QUOTES, 'UTF-8');
        $content = htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
        return '<meta property="' . $property . '" content="' . $content . '" />';
    }


    /**
     * Get the URL for a theme asset.
     * @param string $path
     * @return string
     */
    public static function getThemeUrl($path = '')
    {
        return url_origin() . self::$themeAssetsPath[self::$currentArea] . self::$theme . '/' . $path;
    }

    /**
     * Get the asset paths for all areas.
     * @return array
     */
    public static function getAssetsPaths()
    {
        return self::$themeAssetsPath;
    }
}