<?php

namespace Zen\Core;

/**
 * Class Validate
 * Provides static methods for validating data against rules and collecting errors.
 */
class Validate
{
    /**
     * @var array Validation errors.
     */
    protected static $errors = [];
    /**
     * @var array Validation messages.
     */
    protected static $messages = [];
    /**
     * @var array Custom validation messages.
     */
    protected static $customMessages = [];
    /**
     * @var array Data being validated.
     */
    protected static $data = [];

    /**
     * Validate data against rules.
     * @param array $data
     * @param array $rules
     * @param array $messages
     * @return bool True if validation passes, false otherwise.
     */
    public static function make(array $data, array $rules, array $messages = []): bool
    {
        self::$errors = [];
        self::$data = $data;
        self::$customMessages = $messages;

        foreach ($rules as $field => $rule) {
            self::applyRule($field, $data, $rule);
        }

        return empty(self::$errors);
    }

    /**
     * Apply validation rules to a field.
     * @param string $field
     * @param array $data
     * @param string $rule
     */
    protected static function applyRule(string $field, array $data, string $rule): void
    {
        $ruleParts = explode('|', $rule);

        foreach ($ruleParts as $part) {
            $params = [];
            if (strpos($part, ':') !== false) {
                [$method, $paramString] = explode(':', $part, 2);
                $params = explode(',', $paramString);
            } else {
                $method = $part;
            }

            $value = $data[$field] ?? null;

            if (!self::validateRule($method, $field, $value, $params)) {
                self::addError($field, $method, $params);
            }
        }
    }

    /**
     * Validate a rule against a field's value.
     * @param string $method
     * @param string $field
     * @param mixed $value
     * @param array $params
     * @return bool True if the rule passes, false otherwise.
     */
    protected static function validateRule(string $method, string $field, $value, array $params): bool
    {
        switch ($method) {
            case 'required':
                return isset(self::$data[$field]) && !empty($value);
            
            case 'email':
                return empty($value) || filter_var($value, FILTER_VALIDATE_EMAIL);
            
            case 'min':
                return empty($value) || strlen($value) >= ($params[0] ?? 0);
            
            case 'max':
                return empty($value) || strlen($value) <= ($params[0] ?? 0);
            
            case 'numeric':
                return empty($value) || is_numeric($value);
            
            case 'alpha':
                return empty($value) || ctype_alpha($value);
            
            case 'alpha_num':
                return empty($value) || ctype_alnum($value);
            
            case 'date':
                return empty($value) || strtotime($value) !== false;
            
            case 'url':
                return empty($value) || filter_var($value, FILTER_VALIDATE_URL);
            
            case 'ip':
                return empty($value) || filter_var($value, FILTER_VALIDATE_IP);
            
            case 'between':
                if (count($params) !== 2) return false;
                return empty($value) || ($value >= $params[0] && $value <= $params[1]);
            
            case 'in':
                return empty($value) || in_array($value, $params);
            
            case 'unique':
                if (empty($value)) return true;
                if (strpos($params[0], ':') !== false) {
                    [$table, $column] = explode(':', trim($params[0], '()'));
                } else {
                    [$table, $column] = [$params[0], $field];
                }

                $stmt = \Zen\Core\Db::dbal()->prepare("SELECT $column FROM $table WHERE $column = :value LIMIT 1",);
                $stmt->bindValue("value", $value);
                $resultSet = $stmt->executeQuery();

                // var_dump($resultSet->fetchOne());

                return $resultSet->fetchOne() === false;
            
            default:
                return false;
        }
    }

    /**
     * Add an error message for a field.
     * @param string $field
     * @param string $rule
     * @param array $params
     */
    protected static function addError(string $field, string $rule, array $params = []): void
    {
        if (!isset(self::$errors[$field])) {
            self::$errors[$field] = [];
        }

        $message = self::getErrorMessage($field, $rule, $params);
        self::$errors[$field][] = $message;
    }

    /**
     * Get the error message for a field and rule.
     * @param string $field
     * @param string $rule
     * @param array $params
     * @return string The error message.
     */
    protected static function getErrorMessage(string $field, string $rule, array $params): string
    {
        // Check for custom message
        $customKey = "$field.$rule";
        if (isset(self::$customMessages[$customKey])) {
            $message = self::$customMessages[$customKey];
        } else {
            $message = self::getDefaultMessage($field, $rule);
        }

        // Replace parameters in message
        $message = str_replace(':attribute', $field, $message);
        foreach ($params as $key => $value) {
            $message = str_replace(":param$key", $value, $message);
        }

        return $message;
    }

    /**
     * Get the default error message for a rule.
     * @param string $field
     * @param string $rule
     * @return string The default error message.
     */
    protected static function getDefaultMessage(string $field, string $rule): string
    {
        $messages = [
            'required' => 'The :attribute field is required.',
            'email' => 'The :attribute must be a valid email address.',
            'min' => 'The :attribute must be at least :param0 characters.',
            'max' => 'The :attribute may not be greater than :param0 characters.',
            'numeric' => 'The :attribute must be a number.',
            'alpha' => 'The :attribute may only contain letters.',
            'alpha_num' => 'The :attribute may only contain letters and numbers.',
            'date' => 'The :attribute is not a valid date.',
            'url' => 'The :attribute format is invalid.',
            'ip' => 'The :attribute must be a valid IP address.',
            'between' => 'The :attribute must be between :param0 and :param1.',
            'in' => 'The selected :attribute is invalid.',
            'unique' => 'The :attribute has already been taken.',
            'invalid_rule' => 'Invalid validation rule for :attribute.'
        ];

        return $messages[$rule] ?? "The :attribute field is invalid.";
    }

    /**
     * Get the validation errors.
     * @return array Array of validation errors.
     */
    public static function getErrors(): array
    {
        return self::$errors;
    }

    /**
     * Check if validation has failed.
     * @return bool True if there are validation errors, false otherwise.
     */
    public static function fails(): bool
    {
        return !empty(self::$errors);
    }

    /**
     * Check if validation has passed.
     * @return bool True if there are no validation errors, false otherwise.
     */
    public static function passes(): bool
    {
        return empty(self::$errors);
    }
}
