<?php

namespace Zen\Core;

use Zen\Core\Error;

/**
 * Class Zen
 * Main application bootstrap and utility class for configuration, routing, and error handling.
 */
class Zen
{
    /**
     * @var string Application version.
     */
    protected static $version = '1.0.0';
    /**
     * @var array|null Application configuration cache.
     */
    protected static $appConfig = null;

    /**
     * Begin the application process (placeholder).
     * @return void
     */
    public static function beginProcess()
    {
        //
    }

    /**
     * Terminate the application process and log performance if in testing environment.
     * @return void
     */
    public static function terminateProcess()
    {
        if (self::loadConfig('environment') == 'testing') {
            // Record end time
            $end_time = hrtime(true);

            // Calculate elapsed time in nanoseconds
            $elapsed_time_ns = $end_time - zen_start;

            // Convert nanoseconds to seconds
            $elapsed_time_sec = $elapsed_time_ns / 1e9;

            // Convert to milliseconds
            $elapsed_time_ms = $elapsed_time_ns / 1e6;

            Error::createLog("performance", "Url: " . full_url() . "\n" . "Elapsed time: " . $elapsed_time_sec . "s (".$elapsed_time_ms."ms, ".$elapsed_time_ns."ns) \n");
        }
    }

    /**
     * Load application configuration.
     *
     * @param string $area Specific configuration area to load (optional).
     * @return array|null Configuration array or null if not found.
     */
    public static function loadConfig($area = '')
    {
        try {
            if (is_null(self::$appConfig)) {
                self::$appConfig = include(ZEN_PATH . '/app/config.php');
            }
            return empty($area) ? self::$appConfig : self::$appConfig[$area];
        } catch (\Throwable $e) {
            Error::print('There\'s an error in the config file, ' . $e->getMessage() . ' [line: ' . $e->getLine() . ']');
        }
    }

    public static function checkAboutFile($file_content)
    {
        // Attempt to decode JSON
        $data = json_decode($file_content, true);

        // If JSON is invalid, return false
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($data)) {
            return false;
        }

        // Required fields
        $required = ['type', 'id', 'name', 'description', 'version', 'author'];
        
        // Check if all required fields are present
        foreach ($required as $key) {
            if (empty($data[$key]) || !is_string($data[$key])) {
                return false;
            }
        }

        // Allowed types
        $allowedTypes = ['web', 'email', 'admin', 'plugin', 'language'];

        // Check if type is valid
        if (!in_array($data['type'], $allowedTypes, true)) {
            return false;
        }

        // All checks passed
        return true;
    }


    /**
     * Load application routes.
     *
     * @return void
     */
    public static function loadRoutes()
    {
        try {
            require_once(ZEN_PATH . "/app/router.php");
        } catch (\Throwable $e) {
            Error::print('There\'s an error in the router file, ' . $e->getMessage() . ' [line: ' . $e->getLine() . ']');
        }
    }

    /**
     * Load application layers.
     *
     * @return void
     */
    public static function loadLayers()
    {
        try {
            require_once(ZEN_PATH . "/app/layers.php");
        } catch (\Throwable $e) {
            Error::print('There\'s an error in the layers file, ' . $e->getMessage() . ' [line: ' . $e->getLine() . ']');
        }
    }

    /**
     * Set custom error handler for the application.
     *
     * @return void
     */
    public static function setErrorHandler()
    {
        set_error_handler(
            function ($errNo, $errStr, $errFile, $errLine) {
                throw new \ErrorException($errStr, $errNo, 0, $errFile, $errLine);
            }
        );
    }

    /**
     * Get the application version.
     *
     * @return string Application version.
     */
    public static function getVersion()
    {
        return self::$version;
    }

    /**
     * Serve a file to the client with support for partial content (range requests).
     *
     * @param string $fullPath Full path to the file to be served.
     * @return void
     */
    public static function serveFile($fullPath) {
        // Validate file exists and is readable
        if (!Storage::exists($fullPath) || !is_readable($fullPath)) {
            Error::notFound();
        }
    
        // Validate the requested file
        if (!self::validateRequestedFile($fullPath)) {
            return;
        }

        $size = filesize($fullPath);
        $mime = mime_content_type($fullPath);
    
        // Initialize range parameters
        $start = 0;
        $end = $size - 1;
        $length = $size;
    
        // Parse HTTP range header
        if (isset($_SERVER['HTTP_RANGE'])) {
            $range = $_SERVER['HTTP_RANGE'];
            if (preg_match('/bytes=(\d+)-(\d+)?/', $range, $matches)) {
                $start = intval($matches[1]);
                $end = isset($matches[2]) ? intval($matches[2]) : $size - 1;
    
                // Validate range
                if ($start < 0 || $end >= $size || $start > $end) {
                    http_response_code(416); // Requested Range Not Satisfiable
                    exit;
                }
    
                $length = $end - $start + 1;
                http_response_code(206); // Partial Content
            }
        }
    
        // Set headers for file streaming
        header("Content-Type: $mime");
        header("Content-Length: $length");
        header('Content-Disposition: inline');
        header('Accept-Ranges: bytes');
        header("Content-Range: bytes $start-$end/$size");
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
    
        // Open and stream file
        $file = fopen($fullPath, 'rb');
        if (!$file) {
            Error::print('Could not open file');
        }
    
        // Seek to start position
        fseek($file, $start);
    
        // Stream file in chunks
        $chunkSize = 32768; // 32KB chunks
        $bytesSent = 0;
        ob_clean();
        while (!feof($file) && $bytesSent < $length) {
            $readSize = min($chunkSize, $length - $bytesSent);
            echo fread($file, $readSize);
            $bytesSent += $readSize;
            flush();
            
            // Prevent script timeout
            set_time_limit(30);
        }
    
        fclose($file);
        exit;
    }

    /**
     * Validate the requested file for security and existence.
     *
     * @param string $requestedFile The requested file path.
     * @return bool True if the file is valid and accessible, false otherwise.
     */
    private static function validateRequestedFile($requestedFile) {
        // Forbidden extensions to block
        $forbiddenExtensions = [
            '.php', '.htaccess', '.phtml', '.phps', '.phar', '.conf', 
            '.exe', '.dll', '.bat', '.cmd', '.sh', '.pl', '.cgi'
        ];
        
        // Validate against forbidden extensions
        foreach ($forbiddenExtensions as $ext) {
            if (stripos($requestedFile, $ext) !== false) {
                error_log("Blocked potentially malicious file request: $requestedFile");
                Error::print('Access denied', true, 403);
            }
        }
        
        // Sanitize file path to prevent directory traversal
        $safePath = realpath($requestedFile);
        $allowedDirectory = realpath($_SERVER['DOCUMENT_ROOT']);
        
        if ($safePath === false || strpos($safePath, $allowedDirectory) !== 0) {
            error_log("Potential directory traversal attempt: $requestedFile");
            Error::print('Invalid file path', true, 403);
        }
        
        return true;
    }

    /**
     * Route resources such as assets and plugins.
     *
     * @return void
     */
    public static function routeResources()
    {
        if (php_sapi_name() === 'cli-server') {
            $timestamp = date('Y-m-d H:i:s');
            $request = "{$_SERVER['REQUEST_METHOD']} {$_SERVER['REQUEST_URI']}";
            $client = $_SERVER['REMOTE_ADDR'];

            // Log to CLI
            error_log("[{$timestamp}] {$client} {$request}");

            // Get the full path of the requested file
            $requestedFile = $_SERVER['DOCUMENT_ROOT'] . parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

            // Check if the requested file exists and is a file
            if (Storage::exists($requestedFile)) {
                self::serveFile($requestedFile);
            }

            // Check the requested URI
            $requestUri = $_SERVER['REQUEST_URI'];

            // Define base paths for your resources
            $basePaths = [
                'assets' => 'resources/themes/web/',
                'admin-assets' => 'resources/themes/admin/',
                'email-assets' => 'resources/themes/email/',
                'plugin-assets' => 'resources/plugins/',
            ];

            // Parse the URI to match your routes
            $matches = [];
            if (preg_match('~^(assets|admin-assets|email-assets|plugin-assets)/([^/]+)/(.+)$~', ltrim($requestUri, '/'), $matches)) {
                $type = $matches[1];
                $themeOrPlugin = $matches[2];
                $filePath = $matches[3];

                $basePath = $basePaths[$type] ?? null;
                if ($basePath) {
                    $fullPath = $basePath . $themeOrPlugin . '/public/' . $filePath;
                    
                    // Validate the full path before serving
                    if (self::validateRequestedFile($fullPath)) {
                        self::serveFile($fullPath);
                    }
                }
            }
        }
    }
}
