<?php

namespace Zen;

use Zen\Core\Zen;
use Zen\Core\Router;
use Zen\Core\Error;
use Zen\Core\Defer;

/* Zen Framework Core Engine */
class Engine extends Zen
{
    public function __construct()
    {
        self::beginProcess();
    }

    public static function run()
    {
        self::setErrorHandler();
        try {
            // load routes
            self::loadRoutes();

            // load routes
            self::loadLayers();

            // route resources
            self::routeResources();

            // Route the request
            Router::call(
                Router::route($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'])
            );
        } catch (\Throwable $e) {
            Error::print($e->getMessage() . PHP_EOL . ' [file: <a href="vscode://file/'.$e->getFile() . ':' . $e->getLine() .'" >' . $e->getFile() . '</a> line: ' . $e->getLine() . ']');
        }
    }

    public static function load()
    {
        // load routes
        self::loadRoutes();

        // load routes
        self::loadLayers();
    }

    public function __destruct()
    {
        self::terminateProcess();

        register_shutdown_function(function () {
            // Flush output buffer and send the response to the browser
            if (function_exists('fastcgi_finish_request')) {
                fastcgi_finish_request();
            } else {
                @ob_end_flush();
                flush();
            }

            // Execute deferred tasks
            Defer::execute();
        });
    }
}
