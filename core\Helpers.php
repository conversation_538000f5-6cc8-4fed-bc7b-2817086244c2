<?php

function setting($key = null, $default = '')
{
    return \Zen\Core\Settings::get($key) ?? $default;
}

function s($key = null, $default = '')
{
    return setting($key, $default);
}

function _s($key = null, $default = '')
{
    echo setting($key, $default);
}

function router($name, $params = [], $fullUrl = true)
{
    return ($fullUrl ? url_origin() : '') . \Zen\Core\Router::generate($name, $params);
}

function r($name, $params = [], $fullUrl = true)
{
    return router($name, $params, $fullUrl);
}

function _r($name, $params = [], $fullUrl = true)
{
    echo router($name, $params, $fullUrl);
}

function lang($key = null, $default = '', $section = 'global')
{
    return \Zen\Core\Language::get($key, $default, $section) ?? $default;
}

function l($key = null, $default = '', $section = 'global')
{
    return lang($key, $default, $section);
}

function _l($key = null, $default = '', $section = 'global')
{
    echo lang($key, $default, $section);
}

function tl($key = null, $default = '')
{
    $template = \Zen\Core\Theme::getCurrentTheme('web');
    return lang($key, $default, 'theme:web:' . $template) ?? l($key, $default, 'global');
}

function _tl($key = null, $default = '')
{
    echo tl($key, $default) ?? l($key, $default, 'global');
}

function ml($key = null, $default = '')
{
    $template = \Zen\Core\Theme::getCurrentTheme('email');
    return lang($key, $default, 'theme:email:' . $template) ?? l($key, $default, 'global');
}

function _ml($key = null, $default = '')
{
    echo ml($key, $default) ?? l($key, $default, 'global');
}

function al($key = null, $default = '')
{
    $template = \Zen\Core\Theme::getCurrentTheme('admin');
    return lang($key, $default, 'theme:admin:' . $template) ?? l($key, $default, 'global');
}

function _al($key = null, $default = '')
{
    echo al($key, $default) ?? l($key, $default, 'global');
}

// we still  need to figure out the plugin class first
// function pl($key = null, $default = '', $plugin = '')
// {
//     return lang($key, $default, 'plugin:' . $plugin) ?? l($key, $default, 'global');
// }

// function _pl($key = null, $default = '', $plugin = '')
// {
//     echo pl($key, $default, $plugin) ?? l($key, $default, 'global');
// }
