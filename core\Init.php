<?php

if (!function_exists('str_starts_with')) {
    function str_starts_with($haystack, $needle)
    {
        return strncmp($haystack, $needle, strlen($needle)) === 0;
    }
}

if (!function_exists('url_origin')) {
    function url_origin($defaultProtocol = '')
    {
        $is_ssl = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on');
        $server_protocol = strtolower($_SERVER['SERVER_PROTOCOL']);
        $protocol = substr($server_protocol, 0, strpos($server_protocol, '/')) . (($is_ssl) ? 's' : '');
        $server_port = $_SERVER['SERVER_PORT'];
        $port = ((!$is_ssl && $server_port == '80') || ($is_ssl && $server_port == '443')) ? '' : ':' . $server_port;
        $host = (isset($_SERVER['HTTP_X_FORWARDED_HOST'])) ? $_SERVER['HTTP_X_FORWARDED_HOST'] : (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : null);
        $host = isset($host) ? $host : $_SERVER['SERVER_NAME'] . $port;
        return (!empty($defaultProtocol) ? $defaultProtocol : $protocol) . '://' . $host;
    }
}

if (!function_exists('full_url')) {
    function full_url($defaultProtocol = '')
    {
        return url_origin($defaultProtocol) . $_SERVER['REQUEST_URI'];
    }
}


if (!function_exists('redirect')) {
    function redirect($url, $statusCode = 302)
    {
        header('Location: ' . $url, true, $statusCode);
        exit;
    }
}

if (!function_exists('dd')) {
    function dd(...$vars)
    {
        echo '<pre>';
        foreach ($vars as $var) {
            var_dump($var);
        }
        echo '</pre>';
        exit;
    }
}

if (!function_exists('d')) {
    function d(...$vars)
    {
        echo '<pre>';
        foreach ($vars as $var) {
            var_dump($var);
        }
        echo '</pre>';
    }
}

if (!function_exists('view')) {
    function view($view, $data = [])
    {
        return \Zen\Core\Theme::render($view, $data);
    }
}

if (!function_exists('asset')) {
    function asset($path)
    {
        return \Zen\Core\Theme::getThemeUrl($path);
    }
}

if (!function_exists('config')) {
    function config($key, $default = null)
    {
        return \Zen\Core\Zen::loadConfig($key) ?? $default;
    }
}

if (!function_exists('__')) {
    function __($key, $default = null)
    {
        return \Zen\Core\Language::get($key, $default);
    }
}

if (!function_exists('route')) {
    function route($name, $params = [])
    {
        return \Zen\Core\Router::generate($name, $params);
    }
}

if (!function_exists('session')) {
    function session($key, $default = null)
    {
        return \Core\Session::get($key, $default);
    }
}

if (!function_exists('auth')) {
    function auth($guard = 'user')
    {
        return \Core\Auth::guard($guard);
    }
}

if (!function_exists('request')) {
    function request()
    {
        return new \Zen\Core\Http\Request();
    }
}

if (!function_exists('response')) {
    function response($content = '', $status = 200, $headers = [])
    {
        return new \Zen\Core\Http\Response($content, $status, $headers);
    }
}

if (!function_exists('now')) {
    function now($format = 'Y-m-d H:i:s')
    {
        return date($format);
    }
}

if (!function_exists('today')) {
    function today($format = 'Y-m-d')
    {
        return date($format);
    }
}

if (!function_exists('csrf_token')) {
    function csrf_token()
    {
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}

if (!function_exists('csrf_field')) {
    function csrf_field()
    {
        return '<input type="hidden" name="_token" value="' . csrf_token() . '">';
    }
}

if (!function_exists('method_field')) {
    function method_field($method)
    {
        return '<input type="hidden" name="_method" value="' . strtoupper($method) . '">';
    }
}

if (!function_exists('old')) {
    function old($key, $default = null)
    {
        return $_REQUEST[$key] ?? $default;
    }
}

if (!function_exists('collect')) {
    function collect($array)
    {
        return new \Illuminate\Support\Collection($array);
    }
}

if (!function_exists('env')) {
    function env($key, $default = null)
    {
        return $_ENV[$key] ?? $default;
    }
}

if (!function_exists('logger')) {
    function logger($message, $level = 'info')
    {
        // Placeholder for a more robust logging implementation
        error_log("[$level] $message");
    }
}

if (!function_exists('event')) {
    function event($name, $payload = [])
    {
        \Zen\Core\Event::trigger($name, $payload);
    }
}

if (!function_exists('cache')) {
    function cache($key, $value = null, $minutes = 60)
    {
        if ($value === null) {
            return \Zen\Core\Cache::get($key);
        }
        return \Zen\Core\Cache::set($key, $value, $minutes);
    }
}

if (!function_exists('bcrypt')) {
    function bcrypt($value)
    {
        return \Zen\Core\Hash::make($value);
    }
}

if (!function_exists('value')) {
    function value($value)
    {
        return $value instanceof \Closure ? $value() : $value;
    }
}

if (!function_exists('with')) {
    function with($object)
    {
        return $object;
    }
}

if (!function_exists('class_basename')) {
    function class_basename($class)
    {
        $class = is_object($class) ? get_class($class) : $class;
        return basename(str_replace('\\', '/', $class));
    }
}

if (!function_exists('trait_uses_recursive')) {
    function trait_uses_recursive($trait)
    {
        $traits = class_uses($trait);
        foreach ($traits as $t) {
            $traits += trait_uses_recursive($t);
        }
        return $traits;
    }
}

if (!function_exists('class_uses_recursive')) {
    function class_uses_recursive($class)
    {
        $traits = class_uses($class);
        foreach ($traits as $trait) {
            $traits += trait_uses_recursive($trait);
        }
        return $traits;
    }
}

if (!function_exists('str_random')) {
    function str_random($length = 16)
    {
        return bin2hex(random_bytes($length / 2));
    }
}

if (!function_exists('str_slug')) {
    function str_slug($title, $separator = '-')
    {
        $title = preg_replace('/[^a-zA-Z0-9\s]/', '', $title);
        $title = str_replace(' ', $separator, $title);
        return strtolower($title);
    }
}

if (!function_exists('str_limit')) {
    function str_limit($value, $limit = 100, $end = '...')
    {
        if (mb_strwidth($value, 'UTF-8') <= $limit) {
            return $value;
        }
        return rtrim(mb_strimwidth($value, 0, $limit, '', 'UTF-8')) . $end;
    }
}

if (!function_exists('str_contains')) {
    function str_contains($haystack, $needles)
    {
        foreach ((array) $needles as $needle) {
            if ($needle !== '' && mb_strpos($haystack, $needle) !== false) {
                return true;
            }
        }
        return false;
    }
}

if (!function_exists('str_finish')) {
    function str_finish($value, $cap)
    {
        $quoted = preg_quote($cap, '/');
        return preg_replace('/(?:' . $quoted . ')+$/u', '', $value) . $cap;
    }
}

if (!function_exists('str_is')) {
    function str_is($pattern, $value)
    {
        $patterns = is_array($pattern) ? $pattern : (array) $pattern;
        if (empty($patterns)) {
            return false;
        }
        foreach ($patterns as $p) {
            if ($p == $value) {
                return true;
            }
            $p = preg_quote($p, '#');
            $p = str_replace('\*', '.*', $p);
            if (preg_match('#^' . $p . '\z#u', $value) === 1) {
                return true;
            }
        }
        return false;
    }
}

if (!function_exists('str_replace_array')) {
    function str_replace_array($search, array $replace, $subject)
    {
        $segments = explode($search, $subject);
        $result = array_shift($segments);
        foreach ($segments as $segment) {
            $result .= (array_shift($replace) ?? $search) . $segment;
        }
        return $result;
    }
}

if (!function_exists('studly_case')) {
    function studly_case($value)
    {
        $value = ucwords(str_replace(['-', '_'], ' ', $value));
        return str_replace(' ', '', $value);
    }
}

if (!function_exists('snake_case')) {
    function snake_case($value, $delimiter = '_')
    {
        if (!ctype_lower($value)) {
            $value = preg_replace('/\s+/u', '', ucwords($value));
            $value = strtolower(preg_replace('/(.)(?=[A-Z])/u', '$1' . $delimiter, $value));
        }
        return $value;
    }
}

if (!function_exists('camel_case')) {
    function camel_case($value)
    {
        return lcfirst(studly_case($value));
    }
}

if (!function_exists('title_case')) {
    function title_case($value)
    {
        return mb_convert_case($value, MB_CASE_TITLE, 'UTF-8');
    }
}

if (!function_exists('array_add')) {
    function array_add($array, $key, $value)
    {
        if (!isset($array[$key])) {
            $array[$key] = $value;
        }
        return $array;
    }
}

if (!function_exists('array_collapse')) {
    function array_collapse($array)
    {
        $results = [];
        foreach ($array as $values) {
            if ($values instanceof \Illuminate\Support\Collection) {
                $values = $values->all();
            } elseif (!is_array($values)) {
                continue;
            }
            $results = array_merge($results, $values);
        }
        return $results;
    }
}

if (!function_exists('array_divide')) {
    function array_divide($array)
    {
        return [array_keys($array), array_values($array)];
    }
}

if (!function_exists('array_dot')) {
    function array_dot($array, $prepend = '')
    {
        $results = [];
        foreach ($array as $key => $value) {
            if (is_array($value) && !empty($value)) {
                $results = array_merge($results, array_dot($value, $prepend . $key . '.'));
            } else {
                $results[$prepend . $key] = $value;
            }
        }
        return $results;
    }
}

if (!function_exists('array_except')) {
    function array_except($array, $keys)
    {
        return array_diff_key($array, array_flip((array) $keys));
    }
}

if (!function_exists('array_first')) {
    function array_first($array, callable $callback = null, $default = null)
    {
        if (is_null($callback)) {
            if (empty($array)) {
                return value($default);
            }
            foreach ($array as $item) {
                return $item;
            }
        }
        foreach ($array as $key => $value) {
            if (call_user_func($callback, $value, $key)) {
                return $value;
            }
        }
        return value($default);
    }
}

if (!function_exists('array_last')) {
    function array_last($array, callable $callback = null, $default = null)
    {
        if (is_null($callback)) {
            return empty($array) ? value($default) : end($array);
        }
        return array_first(array_reverse($array, true), $callback, $default);
    }
}

if (!function_exists('array_flatten')) {
    function array_flatten($array, $depth = INF)
    {
        $result = [];
        foreach ($array as $item) {
            if (!is_array($item)) {
                $result[] = $item;
            } elseif ($depth === 1) {
                $result = array_merge($result, array_values($item));
            } else {
                $result = array_merge($result, array_flatten($item, $depth - 1));
            }
        }
        return $result;
    }
}

if (!function_exists('array_forget')) {
    function array_forget(&$array, $keys)
    {
        $original = &$array;
        $keys = (array) $keys;
        if (count($keys) === 0) {
            return;
        }
        foreach ($keys as $key) {
            if (array_key_exists($key, $array)) {
                unset($array[$key]);
                continue;
            }
            $parts = explode('.', $key);
            $array = &$original;
            while (count($parts) > 1) {
                $part = array_shift($parts);
                if (isset($array[$part]) && is_array($array[$part])) {
                    $array = &$array[$part];
                } else {
                    continue 2;
                }
            }
            unset($array[array_shift($parts)]);
        }
    }
}

if (!function_exists('array_get')) {
    function array_get($array, $key, $default = null)
    {
        if (is_null($key)) {
            return $array;
        }
        if (isset($array[$key])) {
            return $array[$key];
        }
        foreach (explode('.', $key) as $segment) {
            if (!is_array($array) || !array_key_exists($segment, $array)) {
                return value($default);
            }
            $array = $array[$segment];
        }
        return $array;
    }
}

if (!function_exists('array_has')) {
    function array_has($array, $key)
    {
        if (empty($array) || is_null($key)) {
            return false;
        }
        if (array_key_exists($key, $array)) {
            return true;
        }
        foreach (explode('.', $key) as $segment) {
            if (!is_array($array) || !array_key_exists($segment, $array)) {
                return false;
            }
            $array = $array[$segment];
        }
        return true;
    }
}

if (!function_exists('array_only')) {
    function array_only($array, $keys)
    {
        return array_intersect_key($array, array_flip((array) $keys));
    }
}

if (!function_exists('array_pluck')) {
    function array_pluck($array, $value, $key = null)
    {
        $results = [];
        foreach ($array as $item) {
            $itemValue = data_get($item, $value);
            if (is_null($key)) {
                $results[] = $itemValue;
            } else {
                $itemKey = data_get($item, $key);
                $results[$itemKey] = $itemValue;
            }
        }
        return $results;
    }
}

if (!function_exists('array_pull')) {
    function array_pull(&$array, $key, $default = null)
    {
        $value = array_get($array, $key, $default);
        array_forget($array, $key);
        return $value;
    }
}

if (!function_exists('array_set')) {
    function array_set(&$array, $key, $value)
    {
        if (is_null($key)) {
            return $array = $value;
        }
        $keys = explode('.', $key);
        while (count($keys) > 1) {
            $key = array_shift($keys);
            if (!isset($array[$key]) || !is_array($array[$key])) {
                $array[$key] = [];
            }
            $array = &$array[$key];
        }
        $array[array_shift($keys)] = $value;
        return $array;
    }
}

if (!function_exists('array_sort')) {
    function array_sort($array, $callback)
    {
        uasort($array, $callback);
        return $array;
    }
}

if (!function_exists('array_sort_recursive')) {
    function array_sort_recursive($array)
    {
        foreach ($array as &$value) {
            if (is_array($value)) {
                $value = array_sort_recursive($value);
            }
        }
        if (is_array_assoc($array)) {
            ksort($array);
        } else {
            sort($array);
        }
        return $array;
    }
}

if (!function_exists('array_where')) {
    function array_where($array, callable $callback)
    {
        return array_filter($array, $callback, ARRAY_FILTER_USE_BOTH);
    }
}

if (!function_exists('is_array_assoc')) {
    function is_array_assoc(array $array)
    {
        $keys = array_keys($array);
        return array_keys($keys) !== $keys;
    }
}

if (!function_exists('data_get')) {
    function data_get($target, $key, $default = null)
    {
        if (is_null($key)) {
            return $target;
        }
        $key = is_array($key) ? $key : explode('.', $key);
        while (!is_null($segment = array_shift($key))) {
            if ($segment === '*') {
                if ($target instanceof \Illuminate\Support\Collection) {
                    return $target->all();
                } elseif (!is_array($target)) {
                    return value($default);
                }
                $result = [];
                foreach ($target as $item) {
                    $result[] = data_get($item, $key);
                }
                return in_array('*', $key) ? array_collapse($result) : $result;
            }
            if (is_array($target) && array_key_exists($segment, $target)) {
                $target = $target[$segment];
            } elseif (is_object($target) && isset($target->{$segment})) {
                $target = $target->{$segment};
            } else {
                return value($default);
            }
        }
        return $target;
    }
}

if (!function_exists('data_set')) {
    function data_set(&$target, $key, $value, $overwrite = true)
    {
        $segments = is_array($key) ? $key : explode('.', $key);
        if (($segment = array_shift($segments)) === '*') {
            if (!is_array($target)) {
                $target = [];
            }
            if ($segments) {
                foreach ($target as &$inner) {
                    data_set($inner, $segments, $value, $overwrite);
                }
            } elseif ($overwrite) {
                foreach ($target as &$inner) {
                    $inner = $value;
                }
            }
        } elseif (is_array($target)) {
            if ($segments) {
                if (!array_key_exists($segment, $target)) {
                    $target[$segment] = [];
                }
                data_set($target[$segment], $segments, $value, $overwrite);
            } elseif ($overwrite || !array_key_exists($segment, $target)) {
                $target[$segment] = $value;
            }
        } elseif (is_object($target)) {
            if ($segments) {
                if (!isset($target->{$segment})) {
                    $target->{$segment} = [];
                }
                data_set($target->{$segment}, $segments, $value, $overwrite);
            } elseif ($overwrite || !isset($target->{$segment})) {
                $target->{$segment} = $value;
            }
        } else {
            $target = [];
            if ($segments) {
                data_set($target[$segment], $segments, $value, $overwrite);
            } elseif ($overwrite) {
                $target[$segment] = $value;
            }
        }
        return $target;
    }
}

if (!function_exists('head')) {
    function head($array)
    {
        return reset($array);
    }
}

if (!function_exists('last')) {
    function last($array)
    {
        return end($array);
    }
}

if (!function_exists('tap')) {
    function tap($value, $callback)
    {
        $callback($value);
        return $value;
    }
}

if (!function_exists('retry')) {
    function retry($times, callable $callback, $sleep = 0)
    {
        $attempts = 0;
        beginning:
        $attempts++;
        $times--;
        try {
            return $callback($attempts);
        } catch (\Exception $e) {
            if ($times < 1) {
                throw $e;
            }
            if ($sleep) {
                usleep($sleep * 1000);
            }
            goto beginning;
        }
    }
}