# Web Theme Assets
location ~ ^/assets/([^/]+)/(.+)$ {
    alias /resources/themes/web/$1/public/$2;
    try_files $uri =404;
}

# Admin Theme Assets
location ~ ^/admin-assets/([^/]+)/(.+)$ {
    alias /resources/themes/admin/$1/public/$2;
    try_files $uri =404;
}

# Email Theme Assets
location ~ ^/email-assets/([^/]+)/(.+)$ {
    alias /resources/themes/email/$1/public/$2;
    try_files $uri =404;
}

# Plugin Assets
location ~ ^/plugin-assets/([^/]+)/(.+)$ {
    alias /resources/plugins/$1/public/$2;
    try_files $uri =404;
}

# Site
location / {
    if ($http_authorization) {
        set $http_auth_authorization $http_authorization;
    }
	try_files $uri $uri/ /index.php?$args;
}