<h1>Framework Test</h1>

{% if methods is defined %}
    <h2>Available Tests:</h2>
    <ul>
        {% for method in methods %}
            {% if method != '__construct' and method != 'index' %}
                <li><a href="/test/{{ method }}">{{ method }}</a></li>
            {% endif %}
        {% endfor %}
    </ul>
{% endif %}

{% if test is defined %}
    <h2>{{ test }} Test</h2>
    {% if test == 'Language' %}
        <p>Active Language: {{ language }}</p>
        <p>Translated Text: {{ text }}</p>
    {% elseif test == 'Theme' %}
        <p>Active Theme: {{ theme.name }}</p>
        <p>Theme Path: {{ theme.path }}</p>
    {% elseif test == 'Cache' %}
        <p>Cache value for key 'test': {{ value }}</p>
    {% elseif test == 'Session' %}
        <p>Session value for key 'test': {{ value }}</p>
    {% elseif test == 'Hash' %}
        <p>Hashed password: {{ hashed }}</p>
    {% elseif test == 'Storage' %}
        <p>Content of test.txt: {{ content }}</p>
    {% elseif test == 'DB' %}
        <p>Users: {{ users | json_encode }}</p>
    {% elseif test == 'GraphQL' %}
        <p>GraphQL test results</p>
    {% elseif test == 'Validate' %}
        <p>Validation Errors: {{ errors | json_encode }}</p>
    {% else %}
        <p>Test results for {{ test }}</p>
    {% endif %}
{% endif %}